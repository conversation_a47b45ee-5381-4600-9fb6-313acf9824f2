package com.mossbets.integrations.repositories;

import com.mossbets.integrations.controllers.ApiResource;
import com.mossbets.integrations.entities.profile.ProfileBalance;
import com.mossbets.integrations.utils.props.Props;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import io.quarkus.hibernate.orm.panache.PanacheRepository;
import io.vertx.core.impl.logging.LoggerFactory;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@ApplicationScoped
public class ProfileBalanceRepository implements PanacheRepository<ProfileBalance> {
    private static final Logger log = org.slf4j.LoggerFactory.getLogger(ProfileBalanceRepository.class);
    @Inject
    private Props props;

    private static final io.vertx.core.impl.logging.Logger logger = LoggerFactory.getLogger(ProfileBalanceRepository.class);


    public ProfileBalance createProfileBalance(String profileId, BigDecimal balance, BigDecimal bonus,
                                               Integer status, String currency) {
        try {
            ProfileBalance profileBalance = new ProfileBalance();
            profileBalance.setProfileId(profileId);
            profileBalance.setCurrency(currency != null ? currency : "KES");
            profileBalance.setBalance(balance != null ? balance : BigDecimal.ZERO);
            profileBalance.setBonus(bonus != null ? bonus : BigDecimal.ZERO);
            profileBalance.setStatus(status != null ? status : 1);
            profileBalance.setCreatedAt(LocalDateTime.now());
            profileBalance.setUpdatedAt(LocalDateTime.now());
            persist(profileBalance);
            return profileBalance;
        } catch (Exception e) {
            logger.error("Failed to create profile balance for profileId: " + profileId + ", error: " + e.getMessage());
            return null;
        }
    }

    public boolean updateProfileBalance(String profileId, BigDecimal balance, BigDecimal bonus) {
        try {
            ProfileBalance profileBalance = find("profileId", profileId).firstResult();
            if (balance != null) {
                profileBalance.setBalance(balance);
            }
            if (bonus != null) {
                profileBalance.setBonus(bonus);
            }
            profileBalance.setUpdatedAt(LocalDateTime.now());
            persist(profileBalance);
            return true;
        } catch (Exception e) {
            logger.error("Failed to update profile balance for profileId: " + profileId + ", error: " + e.getMessage());
            return false;
        }
    }

    @WithSpan
    public Map<String, Object> getPlayerBalance(String profileId) {
        Map<String, Object> result = new HashMap<>();
        try {
            ProfileBalance profileBalance = find("profileId", profileId).firstResult();
            if (profileBalance == null) {
                result.put("balance", BigDecimal.ZERO);
                result.put("bonus", BigDecimal.ZERO);
            } else {
                result.put("balance", profileBalance.getBalance());
                result.put("bonus", profileBalance.getBonus());
            }
            return result;
        } catch (Exception e) {
            logger.error("Failed to retrieve balance and bonus for profileId: " + profileId + ", error: " + e.getMessage());
            result.put("balance", BigDecimal.ZERO);
            result.put("bonus", BigDecimal.ZERO);
            return result;
        }
    }
    @WithSpan
    @Transactional
    public boolean debitBalance(String profileId, BigDecimal amount, BigDecimal bonus) {
        try {
            int updated = update("balance = balance - ?1, bonus = bonus - ?2, updatedAt = ?3 WHERE profileId = ?4 " +
                            "AND balance >= ?1 AND bonus >= ?2",
                               amount, bonus, LocalDateTime.now(), profileId);
            return updated > 0;
        } catch (Exception e) {
            logger.error("Error debiting balance for profileId: " + profileId, e);
            return false;
        }
    }

    @WithSpan
    @Transactional
    public boolean creditBalance(String profileId, BigDecimal amount, BigDecimal bonus) {
        try {
            int updated = update("balance = balance + ?1, bonus = bonus + ?2, updatedAt = ?3 WHERE profileId = ?4", 
                               amount, bonus, LocalDateTime.now(), profileId);
            if (updated == 0) {
                createProfileBalance(profileId, amount, bonus, 1, props.softCurrency());
            }
            return true;
        } catch (Exception e) {
            logger.error("Error crediting balance for profileId: " + profileId, e);
            return false;
        }
    }
}