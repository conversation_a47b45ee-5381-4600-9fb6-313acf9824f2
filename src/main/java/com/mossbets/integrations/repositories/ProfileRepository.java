package com.mossbets.integrations.repositories;

import com.mossbets.integrations.entities.profile.Profile;
import io.quarkus.hibernate.orm.panache.PanacheRepository;
import io.vertx.core.impl.logging.LoggerFactory;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.HashMap;
import java.util.Map;

@ApplicationScoped
public class ProfileRepository implements PanacheRepository<Profile> {

    private static final io.vertx.core.impl.logging.Logger logger = LoggerFactory.getLogger(ProfileRepository.class);

    public Map<String, Object> getPlayerDetails(String profileId) {
        logger.info("Getting player details for profileId: " + profileId);
        try {
            Profile profile = find("profileId", profileId).firstResult();
            if (profile == null) {
                return null;
            }
            Map<String, Object> profileMap = new HashMap<>();
            profileMap.put("profileId", profile.getProfileId());
            profileMap.put("msisdn", profile.getMsisdn());
            profileMap.put("name", profile.getName() != null ? profile.getName() : "");
            profileMap.put("accNumber", profile.getAccNumber());
            profileMap.put("hash", profile.getHash());
            profileMap.put("network", profile.getNetwork());
            profileMap.put("ipAddress", profile.getIpAddress());
            profileMap.put("status", profile.getStatus());
            
            return profileMap;
        } catch (Exception e) {
            logger.error("Failed to retrieve profile details for profileId: " + profileId + ", error: " + e.getMessage());
            return null;
        }
    }
}