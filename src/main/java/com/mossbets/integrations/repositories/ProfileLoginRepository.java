package com.mossbets.integrations.repositories;

import com.mossbets.integrations.entities.profile.ProfileLogin;
import io.quarkus.hibernate.orm.panache.PanacheRepository;
import io.vertx.core.impl.logging.LoggerFactory;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.HashMap;
import java.util.Map;

@ApplicationScoped
public class ProfileLoginRepository implements PanacheRepository<ProfileLogin> {

    private static final io.vertx.core.impl.logging.Logger logger = LoggerFactory.getLogger(ProfileLoginRepository.class);

    public boolean hasActiveLogin(String profileId) {
        try {
            long profile_id = Long.parseLong(profileId);
            ProfileLogin login = find("profileId = ?1", profile_id).firstResult();
            Map<String, Object> loginMap = new HashMap<>();
            loginMap.put("profileId", login.getProfileId());
            loginMap.put("status", login.getStatus());
            if (loginMap.get("status") != "6" && loginMap.get("profileId") == profileId) {
                return false;
            } else {
                return true;
            }
        } catch (Exception e) {
            logger.error("Failed to retrieve profile login details for profileId: " + profileId);
            return false; // Return false on error
        }
    }

    public ProfileLogin findByAccessToken(String hashedAccessKey) {
        try {
            return find("accessToken = ?1 AND tokenExpiryDate >= NOW() AND status = ?2", hashedAccessKey, 6)
                    .firstResult();
        } catch (Exception e) {
            logger.error("Failed to find profile by access token: " + e.getMessage());
            return null;
        }
    }
}
