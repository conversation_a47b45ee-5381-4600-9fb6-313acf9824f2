package com.mossbets.integrations.repositories;

import com.mossbets.integrations.entities.profile.ProfileOutbox;
import com.mossbets.integrations.entities.profile.ProfileOutbox.MessageType;
import io.quarkus.hibernate.orm.panache.PanacheRepository;
import io.vertx.core.impl.logging.LoggerFactory;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;

import java.time.LocalDateTime;

@ApplicationScoped
public class ProfileOutboxRepository implements PanacheRepository<ProfileOutbox> {

    private static final io.vertx.core.impl.logging.Logger logger = LoggerFactory.getLogger(ProfileOutboxRepository.class);

    @Transactional
    public Long insertOutboxMessage(String profileId, String senderId, MessageType messageType, String message) {
        try {
            ProfileOutbox outbox = new ProfileOutbox();
            outbox.setProfileId(profileId);
            outbox.setSenderId(senderId);
            outbox.setMessageType(messageType);
            outbox.setMessage(message);
            outbox.setStatus(1);
            outbox.setCreatedAt(LocalDateTime.now());
            outbox.setUpdatedAt(LocalDateTime.now());
            persist(outbox);
            return outbox.getId();
        } catch (Exception e) {
            logger.error("Failed to insert outbox message for profileId: " + profileId + ", messageType: " + messageType + ", error: " + e.getMessage());
            return null;
        }
    }
}