package com.mossbets.integrations.repositories;

import com.mossbets.integrations.controllers.ApiResource;
import com.mossbets.integrations.entities.bonus.Promotion;
import io.quarkus.hibernate.orm.panache.PanacheRepository;
import io.vertx.core.impl.logging.LoggerFactory;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import java.util.Optional;
import java.util.Map;
import java.util.HashMap;
import java.util.List;

@ApplicationScoped
public class PromotionRepository implements PanacheRepository<Promotion> {
    private static final io.vertx.core.impl.logging.Logger logger = LoggerFactory.getLogger(ApiResource.class);
    @PersistenceContext(unitName = "bonus")
    EntityManager em;
    
    public Optional<Map<String, Object>> getActiveLeaderboardCampaign() {
        try{
            List<Object[]> results = em.createNativeQuery(
                            "SELECT ps.id, ps.min_stake, p.promo_name, p.promo_url, ps.min_odds, " +
                                    "p.promo_details, p.promo_images " +
                                    "FROM promotion_settings ps " +
                                    "JOIN promotion p ON ps.promo_id = p.id " +
                                    "JOIN promotion_type pt ON p.promo_type_id = pt.id " +
                                    "WHERE pt.component = 'LEADERBOARD' AND pt.status = 1 AND p.status = 1 " +
                                    "AND ps.status = 1 AND NOW() >= p.starting_date AND p.ending_date > NOW() " +
                                    "ORDER BY ps.id ASC")
                    .setMaxResults(1)
                    .getResultList();

            if (!results.isEmpty()) {
                Object[] row = results.get(0);
                Map<String, Object> result = new HashMap<>();
                result.put("id", row[0]);
                result.put("min_stake", row[1]);
                result.put("promo_name", row[2]);
                result.put("promo_url", row[3]);
                result.put("min_odds", row[4]);
                result.put("promo_details", row[5]);
                result.put("promo_images", row[6]);
                return Optional.of(result);
            } else{
                logger.info("No active leaderboard campaign found");
                return Optional.empty();
            }
        }catch(Exception e){
            logger.error("Error getting active leaderboard campaign", e);
            return Optional.empty();
        }

    }
}