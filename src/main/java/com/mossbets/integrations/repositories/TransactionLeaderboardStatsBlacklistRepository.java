package com.mossbets.integrations.repositories;

import com.mossbets.integrations.entities.transactions.TransactionLeaderboardStatsBlacklist;
import io.quarkus.hibernate.orm.panache.PanacheRepository;
import io.vertx.core.impl.logging.LoggerFactory;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.Optional;

@ApplicationScoped
public class TransactionLeaderboardStatsBlacklistRepository implements PanacheRepository<TransactionLeaderboardStatsBlacklist> {

    private static final io.vertx.core.impl.logging.Logger logger = LoggerFactory.
            getLogger(TransactionLeaderboardStatsBlacklistRepository.class);

    public Optional<TransactionLeaderboardStatsBlacklist> findByProfileIdAndStatus(String profileId) {
        try {
            TransactionLeaderboardStatsBlacklist result = find("profileId = ?1 and status = 1", profileId).firstResult();
            return Optional.ofNullable(result);
        } catch (Exception e) {
            logger.error("Failed to retrieve blacklist record for profileId: " + profileId + ", status: 1, error: " + e.getMessage(), e);
            return Optional.empty();
        }
    }
}