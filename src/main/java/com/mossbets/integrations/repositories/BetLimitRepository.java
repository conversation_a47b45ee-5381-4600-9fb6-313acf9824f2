package com.mossbets.integrations.repositories;

import com.mossbets.integrations.entities.bets.BetLimit;
import io.quarkus.hibernate.orm.panache.PanacheRepository;
import jakarta.enterprise.context.ApplicationScoped;
import org.jboss.logging.Logger;

import java.util.HashMap;
import java.util.Map;

@ApplicationScoped
public class BetLimitRepository implements PanacheRepository<BetLimit> {

    private static final Logger logger = Logger.getLogger(BetLimitRepository.class);

    public Map<String, Object> getBetLimit(Integer clientId, String betName) {
        try {
            BetLimit betLimit = find("clientId = ?1 and betName = ?2", clientId, betName).firstResult();
            Map<String, Object> result = new HashMap<>();
            result.put("maxWin", betLimit.getMaxWin());
            result.put("riskApprovalAmount", betLimit.getRiskApprovalAmount());

            return result;
        } catch (Exception e) {
            logger.error("Failed to retrieve bet limit for clientId: " + clientId + ", betName: " + betName + ", error: " + e.getMessage());
            return null;
        }
    }
}