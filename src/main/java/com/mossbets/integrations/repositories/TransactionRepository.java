package com.mossbets.integrations.repositories;

import com.google.gson.JsonObject;
import com.mossbets.integrations.config.TransactionConstants;
import com.mossbets.integrations.controllers.ApiResource;
import com.mossbets.integrations.entities.transactions.Transaction;
import com.mossbets.integrations.utils.props.Props;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import io.quarkus.hibernate.orm.panache.PanacheRepository;
import io.vertx.core.impl.logging.LoggerFactory;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.PersistenceUnit;
import jakarta.transaction.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.HashMap;
import java.util.List;

@ApplicationScoped
public class TransactionRepository implements PanacheRepository<Transaction> {
    @Inject
    private Props props;

    @Inject
    TransactionConstants transactionConstants;

    private static final io.vertx.core.impl.logging.Logger logger = LoggerFactory.getLogger(ApiResource.class);

    public String createTransaction(String profileId, String referenceId, BigDecimal amount,
            String gameDesc, String extraData) {
        try {
            Integer transactionType = amount.compareTo(BigDecimal.ZERO) > 0 ?transactionConstants.
                    TYPE_DEBIT : transactionConstants.TYPE_BONUS;
            String source = amount.compareTo(BigDecimal.ZERO) > 0 ? TransactionConstants.
                    SOURCE_SOFTGAMES_CASH_BET : TransactionConstants.SOURCE_SOFTGAMES_BONUS;
            Transaction transaction = new Transaction();
            transaction.setProfileId(profileId);
            transaction.setReferenceTypeId(props.softStake());
            transaction.setTransactionTypeId(transactionType);
            transaction.setReferenceId(referenceId);
            transaction.setAmount((amount.compareTo(BigDecimal.ZERO) > 0 ? amount.negate(): amount)); // Default from table
            transaction.setCurrency(props.softCurrency()); // Default from table
            transaction.setSource(source);
            transaction.setDescription("SOFTGAMES - " + gameDesc + " Bet Debit");
            transaction.setExtraData(extraData);
            transaction.setCreatedAt(LocalDateTime.now());
            transaction.setUpdatedAt(LocalDateTime.now());
            persist(transaction);
            return transaction.getId().toString();
        } catch (Exception e) {
            logger.error("Failed to create transaction for profileId: " + profileId + ", referenceId: " + referenceId + ", error: " + e.getMessage());
            return null;
        }
    }

    public Transaction findByReferenceIdAndProfileId(String tid, Long profileId) {
        try {
            return find("referenceId = ?1 and profileId = ?2", tid, profileId).firstResult();
        } catch (Exception e) {
            logger.error("Failed to retrieve transaction for referenceId: " + tid + ", profileId: " + profileId + ", error: " + e.getMessage());
            return null;
        }
    }

    public String createRollbackTransaction(String profileId, String referenceId, BigDecimal amount,
                                    String rollbackTid, String extraData, boolean wasDebit) {
        try {
            Transaction transaction = new Transaction();
            transaction.setProfileId(profileId);
            transaction.setReferenceTypeId(props.softVoid());
            transaction.setTransactionTypeId(wasDebit ? TransactionConstants.TYPE_CREDIT : TransactionConstants.TYPE_DEBIT);
            transaction.setReferenceId(referenceId);
            transaction.setAmount((amount.compareTo(BigDecimal.ZERO) > 0 ? amount.negate(): amount)); // Default from table
            transaction.setCurrency(props.softCurrency()); // Default from table
            transaction.setSource(TransactionConstants.SOURCE_SOFTGAMES_ROLLBACK);
            transaction.setDescription("Rollback for TID: " + rollbackTid);
            transaction.setExtraData(extraData);
            transaction.setCreatedAt(LocalDateTime.now());
            transaction.setUpdatedAt(LocalDateTime.now());
            persist(transaction);
            return transaction.getId().toString();
        } catch (Exception e) {
            logger.error("Failed to create transaction for profileId: " + profileId + ", referenceId: " + referenceId + ", error: " + e.getMessage());
            return null;
        }
    }

    public String createCancellationTransaction(String profileId, String referenceId, BigDecimal amount,
                                            String actionId, String extraData) {
        try {
            Transaction transaction = new Transaction();
            transaction.setProfileId(profileId);
            transaction.setReferenceTypeId(props.softRefunds());
            transaction.setTransactionTypeId(TransactionConstants.TYPE_CREDIT);
            transaction.setReferenceId(referenceId);
            transaction.setAmount(amount); // Default from table
            transaction.setCurrency(props.softCurrency()); // Default from table
            transaction.setSource(TransactionConstants.SOURCE_SOFTGAMES_CANCEL);
            transaction.setDescription("Game server cancellation for action: " + actionId);
            transaction.setExtraData(extraData);
            transaction.setCreatedAt(LocalDateTime.now());
            transaction.setUpdatedAt(LocalDateTime.now());
            persist(transaction);
            return transaction.getId().toString();
        } catch (Exception e) {
            logger.error("Failed to create transaction for profileId: " + profileId + ", referenceId: " + referenceId + ", error: " + e.getMessage());
            return null;
        }
    }

    public String createCreditTransaction(String profileId, String referenceId, BigDecimal amount,
                                    String gameDesc, String extraData, String source, String description) {
        try {
            Integer transactionType =1;
            Transaction transaction = new Transaction();
            transaction.setProfileId(profileId);
            transaction.setReferenceTypeId(props.softPayout());
            transaction.setTransactionTypeId(transactionType);
            transaction.setReferenceId(referenceId);
            transaction.setAmount(amount); // Default from table
            transaction.setCurrency(props.softCurrency()); // Default from table
            transaction.setSource(source);
            transaction.setDescription(description);
            transaction.setExtraData(extraData);
            transaction.setCreatedAt(LocalDateTime.now());
            transaction.setUpdatedAt(LocalDateTime.now());
            persist(transaction);
            return transaction.getId().toString();
        } catch (Exception e) {
            logger.error("Failed to create transaction for profileId: " + profileId + ", referenceId: " + referenceId + ", error: " + e.getMessage());
            return null;
        }
    }
}
