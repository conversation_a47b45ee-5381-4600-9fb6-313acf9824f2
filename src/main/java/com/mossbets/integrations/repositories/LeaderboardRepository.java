package com.mossbets.integrations.repositories;

import com.mossbets.integrations.controllers.ApiResource;
import com.mossbets.integrations.entities.transactions.LeaderboardStats;
import io.quarkus.hibernate.orm.panache.PanacheRepository;
import io.vertx.core.impl.logging.LoggerFactory;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@ApplicationScoped
public class LeaderboardRepository implements PanacheRepository<LeaderboardStats> {

    private static final io.vertx.core.impl.logging.Logger logger = LoggerFactory.getLogger(LeaderboardRepository.class);

    @Transactional
    public boolean upsertLeaderboardStats(Integer promoId, String profileId, Long betCount,
                                                              BigDecimal stake, BigDecimal odds, Short status,
                                                              LocalDate promoDate) {
        try {
            LeaderboardStats stats = find("profileId = ?1 and promoDate = ?2", profileId, promoDate).firstResult();
            if (stats == null) {
                stats = new LeaderboardStats();
                stats.setPromoId(promoId);
                stats.setProfileId(profileId);
                stats.setBetCount(betCount != null ? betCount : 0L);
                stats.setStake(stake != null ? stake : BigDecimal.ZERO);
                stats.setOdds(odds != null ? odds : BigDecimal.ZERO);
                stats.setStatus(status != null ? status : (short) 1); // Default from table
                stats.setPromoDate(promoDate);
                stats.setCreatedAt(LocalDateTime.now());
                stats.setUpdatedAt(LocalDateTime.now());
            } else {
                if (betCount != null) {
                    stats.setBetCount(stats.getBetCount() + betCount);
                }
                if (stake != null) {
                    BigDecimal currentStake = stats.getStake() != null ? stats.getStake() : BigDecimal.ZERO;
                    stats.setStake(currentStake.add(stake));
                }
                if (odds != null) {
                    BigDecimal currentOdds = stats.getOdds() != null ? stats.getOdds() : BigDecimal.ZERO;
                    stats.setOdds(currentOdds.add(odds));
                }
                if (status != null) {
                    stats.setStatus(status);
                }
                stats.setUpdatedAt(LocalDateTime.now());
            }
            persist(stats);
            return true;
        } catch (Exception e) {
            logger.error("Failed to upsert leaderboard stats for profileId: " + profileId + ", promoDate: " + promoDate + ", error: " + e.getMessage());
            return false;
        }
    }
    public boolean updateOdds(String profileId, double odds, Long id) {
        try {
            int updated = update("set odds = ?1 where profileId = ?2 and id = ?3",
                    odds, profileId, id);
            return updated > 0;
        } catch (Exception e) {
            logger.error("Failed to update odds: " + e.getMessage());
            return false;
        }
    }
    public Map<String, Object> findByPromoAndProfile(Integer promoId, String profileId) {
        try {
            LeaderboardStats stats = find("promoId = ?1 and profileId = ?2 and promoDate =" +
                    " CURRENT_DATE", promoId, profileId).firstResult();

            logger.info("Stats: " + stats);
            Map<String, Object> result = new HashMap<>();
            result.put("id", stats.getId());
            result.put("odds", stats.getOdds());
            result.put("status", stats.getStatus());
            return result;
        } catch (Exception e) {
            logger.error("Failed to find leaderboard stats: " + e.getMessage());
            return null;
        }
    }
}