package com.mossbets.integrations.repositories;

import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import io.vertx.core.impl.logging.LoggerFactory;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.Query;

@ApplicationScoped
public class ProfileBlacklistRepository implements PanacheRepositoryBase<Object, Long> {

    private static final io.vertx.core.impl.logging.Logger logger = LoggerFactory.getLogger(ProfileBlacklistRepository.class);

    public boolean isBlacklisted(Object profileId) {
        try {
            Query query = getEntityManager().createNativeQuery(
                "SELECT reason, blacklisted_on FROM profile_blacklist WHERE profile_id = ?1 AND status = 3"
            );
            query.setParameter(1, profileId);
            
            Object[] result = (Object[]) query.getSingleResult();
            if (result != null) {
                logger.info("checkBlacklist | ProfileId: " + profileId
                        + " | Reason: " + result[0]
                        + " | BlacklistedOn: " + result[1]);
                return true;
            }
            return false;
            
        } catch (Exception e) {
            return false; // No blacklist record found
        }
    }
}