package com.mossbets.integrations.repositories;

import com.mossbets.integrations.entities.bets.SpribeBet;
import com.mossbets.integrations.utils.props.Props;
import io.quarkus.hibernate.orm.panache.PanacheRepository;
import io.vertx.core.impl.logging.LoggerFactory;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

@ApplicationScoped
public class SpribeBetRepository implements PanacheRepository<SpribeBet> {
    private static final io.vertx.core.impl.logging.Logger logger = LoggerFactory.getLogger(SpribeBetRepository.class);
    @Inject
    private Props props;

    @Transactional
    public SpribeBet insert(SpribeBet bet) {
        persist(bet);
        return bet;
    }

    public Optional<SpribeBet> findBetByBetTransactionId(String betTransactionId) {
        return find("betTransactionId", betTransactionId).firstResultOptional();
    }

    public Optional<SpribeBet> findByBetId(Long betId) {
        return findByIdOptional(betId);
    }

    public Optional<SpribeBet> findByProfileIdAndBetReference(String profileId, String betReference) {
        return find("profileId = ?1 and betReference = ?2", profileId, betReference).firstResultOptional();
    }

    public Optional<SpribeBet> findBetByProfileAndActionId(String profileId, String actionId) {
        return find("profileId = ?1 and actionId = ?2", profileId, actionId).firstResultOptional();
    }

    public Optional<SpribeBet> findBetByProfileAndCreditActionId(String profileId, String creditActionId) {
        return find("profileId = ?1 and creditActionId = ?2", profileId, creditActionId).firstResultOptional();
    }

    @Transactional
    public boolean updateBetStatus(Long betId, Integer status) {
        return update("set status = ?1 where betId = ?2", status, betId) > 0;
    }

    @Transactional
    public Map<String, Object> findDebitBet(String profileId, String providerTxId) {
        try {
            SpribeBet bet = find("profileId = ?1 and status = 0 and betReference = ?2",
                    profileId, providerTxId).firstResult();
            if (bet == null) {
                logger.info("No bet found for profileId: " + profileId + ", gameId: " + providerTxId);
                return null;
            }
            Map<String, Object> betMap = new HashMap<>();
            betMap.put("bet_id", bet.getBetId());
            betMap.put("profile_id", bet.getProfileId());
            betMap.put("bet_credit_transaction_id", bet.getBetCreditTransactionId());
            betMap.put("bet_reference", bet.getBetReference());
            betMap.put("bet_amount", bet.getBetAmount());
            betMap.put("bet_type", bet.getBetType());
            betMap.put("action_id", bet.getActionId());
            betMap.put("extra_data", bet.getExtraData());
            betMap.put("status", bet.getStatus());
            betMap.put("excise_tax", bet.getExciseTax() != null ? bet.getExciseTax() : BigDecimal.ZERO);
            betMap.put("created_by", bet.getCreatedBy() != null ? bet.getCreatedBy() : "");
            betMap.put("balance", bet.getBalance() != null ? bet.getBalance() : "");
            return betMap;
        } catch (Exception e) {
            logger.error("Failed to retrieve bet for profileId: " + profileId + ", gameId: " + providerTxId + ", " +
                    "error: " + e.getMessage(), e);
            return null;
        }
    }
    public List<Map<String, Object>> findBetByCreditProviderID(String tid) {
        try {
            List<SpribeBet> bets = find("creditTid = ?1", tid).list();
            List<Map<String, Object>> result = new ArrayList<>();
            for (SpribeBet bet : bets) {
                Map<String, Object> betMap = new HashMap<>();
                betMap.put("actionId", bet.getActionId());
                betMap.put("creditTid", bet.getCreditProviderId() != null ? bet.getCreditProviderId() : "");
                betMap.put("possibleWin", bet.getPossibleWin());
                betMap.put("status", bet.getStatus());
                result.add(betMap);
            }
            return result;
        } catch (Exception e) {
            logger.error("Failed to retrieve bets for creditTid: " + tid + ", error: " + e.getMessage(), e);
            return new ArrayList<>();
        }
    }


    public String createBet(String profileId, BigDecimal amount,
                            String providerTxId, short betType, String providerName,
                            Short status, String transactionId, String actionId,
                            String Desc, String game,  String extraData, String balance) {

        try {
            SpribeBet bet = new SpribeBet();
            bet.setProfileId(profileId);
            bet.setClientId(props.softClientId()); // Default from table
            bet.setBetCurrency(props.softCurrency()); // Default from table
            bet.setBetAmount(amount != null ? amount : BigDecimal.ZERO);
            bet.setBetReference(providerTxId);
            bet.setBetTransactionId(transactionId);
            bet.setBetType(betType);
            bet.setProviderName(providerName);
            bet.setPossibleWin(BigDecimal.ZERO);
            bet.setCreatedBy(game);
            bet.setActionId(actionId);
            bet.setTotalGames(0); // Default from table
            bet.setTotalOdd(BigDecimal.ZERO);
            bet.setKraReport(status != null ? status : 0);
            bet.setWitholdingTax(BigDecimal.ZERO); // Default from table
            bet.setExciseTax(BigDecimal.ZERO); // Default from table
            bet.setExtraData(extraData);
            bet.setCreatedAt(LocalDateTime.now());
            bet.setUpdatedAt(LocalDateTime.now());
            bet.setBalance(balance);
            persist(bet);
            return String.valueOf(bet);
        } catch (Exception e) {
            logger.error("Failed to create bet for profileId: " + profileId + ", error: " + e.getMessage());
            return null;
        }
    }

    public boolean updateBetStatus(String betId, int status, String extraData, BigDecimal totalOdd,
                                   String providerTxId, String betCreditTransactionId, BigDecimal amount,
                                   String balance) {
        try {
            SpribeBet bet = find("betId", betId).firstResult();
            if (bet == null) {
                logger.warn("No bet found for betId: " + betId);
                return false;
            }
            if (extraData != null) {
                bet.setExtraData(extraData);
            }
            if (!(totalOdd == null)) {
                bet.setTotalOdd(totalOdd);
            }
            if (!(providerTxId == null)) {
                bet.setCreditProviderId(providerTxId);
            }
            if (!(status == 0)) {
                bet.setStatus(status);
            }
            if (betCreditTransactionId != null) {
                bet.setBetCreditTransactionId(betCreditTransactionId);
            }
            if (amount != null) {
                bet.setPossibleWin(amount);
            }
            if (balance != null) {
                bet.setBalance(balance);
            }
            bet.setUpdatedAt(LocalDateTime.now());
            persist(bet);
            return true;
        } catch (Exception e) {
            logger.error("Failed to update bet for betId: " + betId + ", error: " + e.getMessage());
            return false;
        }
    }

    public boolean updateCancelledBetStatus(String betId, int status, String extraData, String balance) {
        try {
            SpribeBet bet = find("betId", betId).firstResult();
            if (bet == null) {
                logger.warn("No bet found for betId: " + betId);
                return false;
            }
            if (!(status == 0)) {
                bet.setStatus(status);
            }
            if (extraData != null) {
                bet.setExtraData(extraData);
            }
            if (balance != null) {
                bet.setBalance(balance);
            }
            bet.setUpdatedAt(LocalDateTime.now());
            persist(bet);
            return true;
        } catch (Exception e) {
            logger.error("Failed to update bet for betId: " + betId + ", error: " + e.getMessage());
            return false;
        }
    }
    public List<Map<String, Object>> findCreditBet(String profileId, String providerTxId) {
        logger.info("findCreditBet | profileId: " + profileId + ", providerTxId: " + providerTxId);
        try {
            List<SpribeBet> bets = find("creditProviderId = ?1 and profileId = ?2", providerTxId, profileId).list();
            List<Map<String, Object>> result = new ArrayList<>();
            for (SpribeBet bet : bets) {
                Map<String, Object> betMap = new HashMap<>();
                betMap.put("betReference", bet.getBetReference());
                betMap.put("betAmount", bet.getBetAmount());
                betMap.put("status", bet.getStatus());
                betMap.put("credit_provider_id", bet.getCreditProviderId() != null ? bet.getCreditProviderId() : "");
                betMap.put("actionId", bet.getActionId());
                logger.info("Our bet result is : " + betMap);
                result.add(betMap);
            }
            return result;
        } catch (Exception e) {
            logger.error("Failed to retrieve bets for providerTxId: " + providerTxId + ", " +
                    "profileId: " + profileId + ", error: " + e.getMessage(), e);
            return new ArrayList<>();
        }
    }
}