package com.mossbets.integrations.repositories;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import org.jboss.logging.Logger;

import java.util.HashMap;
import java.util.Map;

@ApplicationScoped
public class TransactionBetRepository {
    @Inject
    TransactionRepository transactionRepository;

    private static final Logger logger = Logger.getLogger(TransactionBetRepository.class);

    @Inject
    @jakarta.persistence.PersistenceUnit(unitName = "transactions")
    EntityManager transactionsEntityManager;

    public Map<String, Object> findTransactionByTid(String rollbackProviderTxId, String profileId) {
        try {
            String sql = "SELECT t.id, t.profile_id, t.amount, t.currency, t.reference_id, " +
                    "t.transaction_type_id, t.source, t.description, t.extra_data, " +
                    "vb.bet_id, vb.game_id, vb.bet_reference, vb.status " +
                    "FROM mossbets_transactions.transaction t " +
                    "LEFT JOIN mossbets_bets.softgaming_bets vb ON t.reference_id = vb.bet_reference " +
                    "WHERE t.reference_id REGEXP ? AND t.profile_id = ? " +
                    "ORDER BY t.created_at DESC LIMIT 1";

            Object[] result = (Object[]) transactionsEntityManager.createNativeQuery(sql)
                    .setParameter(1, rollbackProviderTxId)
                    .setParameter(2, profileId)
                    .getSingleResult();

            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("id", result[0]); // t.id (Long)
            resultMap.put("profileId", result[1]); // t.profile_id (Long)
            resultMap.put("amount", result[2]); // t.amount (BigDecimal)
            resultMap.put("currency", result[3]); // t.currency (String)
            resultMap.put("referenceId", result[4]); // t.reference_id (String)
            resultMap.put("transactionTypeId", result[5]); // t.transaction_type_id (Integer)
            resultMap.put("source", result[6]); // t.source (String)
            resultMap.put("description", result[7]); // t.description (String)
            resultMap.put("extraData", result[8]); // t.extra_data (String)
            resultMap.put("betId", result[9]); // vb.bet_id (Long)
            resultMap.put("gameId", result[10]); // vb.game_id (String)
            resultMap.put("betReference", result[11]); // vb.bet_reference (String)
            resultMap.put("status", result[12]);// vb.status (Short)

            logger.info("Our bet is : " + resultMap);
            return resultMap;
        } catch (NoResultException e) {
            logger.info("No transaction and bet found for referenceId pattern: " + rollbackProviderTxId + ", profileId: " + profileId);
            return null;
        } catch (Exception e) {
            logger.error("Failed to retrieve transaction and bet for referenceId pattern: " + rollbackProviderTxId + ", profileId: " + profileId + ", error: " + e.getMessage());
            return null;
        }
    }
}