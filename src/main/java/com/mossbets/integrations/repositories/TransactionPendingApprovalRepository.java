package com.mossbets.integrations.repositories;

import com.mossbets.integrations.entities.transactions.TransactionPendingApproval;
import com.mossbets.integrations.utils.props.Props;
import io.quarkus.hibernate.orm.panache.PanacheRepository;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import org.jboss.logging.Logger;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@ApplicationScoped
public class TransactionPendingApprovalRepository implements PanacheRepository<TransactionPendingApproval> {
    @Inject
    private Props props;

    private static final Logger logger = Logger.getLogger(TransactionPendingApprovalRepository.class);

    @Transactional
    public boolean insertPendingApproval(String profileId, String transactionId, BigDecimal amount,
                                      String description) {
        logger.info("Inserting pending approval for profileId: " + profileId + ", transactionId: " + transactionId);
        try {
            TransactionPendingApproval pendingApproval = new TransactionPendingApproval();
            pendingApproval.setProfileId(profileId);
            pendingApproval.setTransactionId(transactionId);
            pendingApproval.setAmount(amount); // Default from table
            pendingApproval.setCurrency(props.softCurrency()); // Default from table
            pendingApproval.setSource("SOFTGAMING");
            pendingApproval.setDescription(description);
            pendingApproval.setStatus(0); // Default from table
            pendingApproval.setCreatedAt(LocalDateTime.now());
            pendingApproval.setUpdatedAt(LocalDateTime.now());
            persist(pendingApproval);
            return true;
        } catch (Exception e) {
            logger.error("Failed to insert pending approval for profileId: " + profileId + ", transactionId: " + transactionId + ", error: " + e.getMessage());
            return false;
        }
    }
}