package com.mossbets.integrations.utils;

import com.google.gson.JsonParser;
import com.mossbets.integrations.config.TransactionConstants;
import com.mossbets.integrations.controllers.resources.Configurations;
import com.mossbets.integrations.entities.profile.ProfileOutbox;
import com.mossbets.integrations.repositories.*;
import com.mossbets.integrations.utils.crypto.HmacService;
import com.mossbets.integrations.utils.props.LeaderboardService;
import com.mossbets.integrations.utils.props.Props;

import com.mossbets.integrations.utils.props.WalletService;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import io.vertx.core.impl.logging.LoggerFactory;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

import com.google.gson.JsonObject;
import jakarta.transaction.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ApplicationScoped
@SuppressWarnings("all")
public class WalletUtils extends Configurations {
    private static final io.vertx.core.impl.logging.Logger logger = LoggerFactory.getLogger(WalletUtils.class);

    @Inject
    private Props props;

    @Inject
    HmacService hmacService;

    @Inject
    WalletService walletService;

    @Inject
    ProfileBalanceRepository profileBalanceRepository;

    @Inject
    LeaderboardService leaderboardService;

    @Inject
    TransactionRepository transactionRepository;

    @Inject
    TransactionBetRepository transactionBetRepository;

    @Inject
    ProfileLoginRepository profileLoginRepository;
    @Inject
    ProfileOutboxRepository profileOutboxRepository;

    @Inject
    Queue Queue;

    @Inject
    TransactionPendingApprovalRepository transactionPendingApprovalRepository;


    @Inject
    ProfileRepository profileRepository;

    /**
     * Gets the player's balance and bonus information from the database
     *
     * @param profileId The ID of the player's profile
     * @return Map containing balance, bonus, and status, or null if not found
     */
    @WithSpan
    @Transactional
    public Map<String, Object> getPlayerBalance(String profileId) {
        return walletService.profileBalance(profileId);
    }

    @WithSpan
    public Map<String, Object> getPlayerDetails(String profileId) {
        Map<String, Object> playerDetails = walletService.playerDetails(profileId);
        logger.info("Player details: " + playerDetails);
        return playerDetails;
    }

    @WithSpan
    public boolean getInfo(String profileId) {
        logger.info("Getting player details for profileId: " + profileId);
        return walletService.getPendingApproval(profileId);
    }

    @WithSpan
    public String updatePlayerBalance(String userId, String provider, String providerTxId, String action, String actionId,
                                      double amount, String game, JsonObject betExtraData, String withdrawProviderTxId, String timestamp) {
        boolean isRisk = false;
        String messageOut=null;
        double odds = 0.0;
        double betAmount = 0.0;
        try {
            boolean isWin = amount > 0;
            int status = isWin ? 1 : 2;
            Map<String, Object> betCheck = walletService.getDebitBet(userId, withdrawProviderTxId);
            if (betCheck == null) {
                logger.info("BetCheck is null");
                String transactionId = processDebit(userId, provider, withdrawProviderTxId, action, actionId, BigDecimal.ZERO,
                            "Credit for Freebet: " + game, game, betExtraData);
                if (transactionId == null) {
                    logger.info("Failed to process debit for freebet. Terminating transaction.");
                    return null;
                }
                logger.info("Successfully processed debit for freebet. Transaction ID: " + transactionId);

                betCheck = walletService.getDebitBet(userId, withdrawProviderTxId);
                logger.info("BetCheck after processing debit for freebet: " + betCheck);
                if (betCheck == null) {
                        logger.info("BetCheck is still null after processing debit for freebet. Terminating transaction.");
                        return null;
                }
            }

            double dbAmount = Double.parseDouble(betCheck.get("bet_amount").toString());
            double tax = Double.parseDouble(betCheck.get("excise_tax").toString());
            double stake = dbAmount - tax;
            if(stake==0){
                odds = 0.0;
            } else{
                odds = amount/stake;
            }
            JsonObject existingExtraData = new JsonObject();
            if (betCheck.get("extra_data") != null) {
                try {
                    existingExtraData = JsonParser.parseString((String) betCheck.get("extra_data")).getAsJsonObject();
                } catch (Exception e) {
                    logger.warn("Failed to parse existing extra data: " + e.getMessage());
                }
            }
            JsonObject balance = new JsonObject();
            if (betCheck.get("balance") != null) {
                try {
                    balance = JsonParser.parseString((String) betCheck.get("balance")).getAsJsonObject();
                } catch (Exception e) {
                    logger.warn("Failed to parse existing extra data: " + e.getMessage());
                }
            }

            if (betExtraData != null) {
                // Merge betExtraData into existingExtraData
                for (String key : betExtraData.keySet()) {
                    existingExtraData.add(key, betExtraData.get(key));
                }
            }
            String transactionId = null;
            BigDecimal maxWinDB = BigDecimal.ZERO;
            BigDecimal riskApprovalLimitDB = BigDecimal.ZERO;

            if (isWin) {
                if (leaderboardService.updateOdds(userId, odds, betAmount)) {
                    logger.info("Successfully updated odds on Leaderboard for user: " + userId);
                }
                double maxWin = amount;
                double riskApproval = 0.0;
                String betName = "";
                if (stake > 0) {
                    betName = "SPRIBE_GAMES";
                } else {
                    betName = "SPRIBE_RAINS";
                }
                Map<String, Object> betLimit = walletService.betLimit(props.softClientId(), betName);
                logger.info("Spribe Bet Limit is :" + betLimit);
                if (betLimit == null) {
                    logger.info("BetLimit is null");
                    maxWinDB = BigDecimal.valueOf(1000000.);
                    riskApprovalLimitDB = BigDecimal.valueOf(1000000.);
                } else{
                     maxWinDB = (BigDecimal) betLimit.get("maxWin");
                     riskApprovalLimitDB = (BigDecimal) betLimit.get("riskApprovalAmount");
                }
                double maxWinLimit = maxWinDB.doubleValue();
                double riskApprovalLimit = riskApprovalLimitDB.doubleValue();
                logger.info("Max Win Limit is :" + maxWinLimit + " and risk approval limit is: " + riskApprovalLimit);
                //use only this
                if (amount > maxWinLimit) {
                    maxWin = maxWinLimit;
                }
                // if (amount > riskApprovalLimit) {
                //     riskApproval = amount - riskApprovalLimit;
                // }
                int transactionTypeId = isWin ? 1 : 2;
                int referenceTypeId;
                referenceTypeId = 4;
                String source = "SOFTGAMES" + (isWin ? "_PAYOUT" : "_ADJUSTMENT");
                String description = "SOFTGAMES - " + game + " Bet " + (isWin ? "Credit" : "Debit");

                transactionId = walletService.createCreditTransaction(userId, providerTxId, BigDecimal.valueOf(maxWin), game,
                        existingExtraData.toString(), source, description);
                if (transactionId == null) {
                    return null;
                } else {
                    Map<String, Object> profileBalance = walletService.profileBalance(userId);
                    if(isWin && (amount>maxWinLimit)){ //changed this
                        messageOut ="Kindly Approve the Transaction "+ transactionId +" for the amount " + maxWin +
                                "game "+"(" + game +")";
                        isRisk = true;
                        walletService.createPendingApproval(userId, transactionId,BigDecimal.valueOf(maxWin), messageOut);
                    }
                    if(!walletService.creditProfileBalance(userId, BigDecimal.valueOf(maxWin), BigDecimal.ZERO)){
                        logger.error("Failed to update profile balance for profileId: " + userId);
                        return null;
                    }
                    String newBalance = walletService.profileBalance(userId).get("balance").toString();
                    balance.addProperty("credit_balance", newBalance);

                    if (!walletService.updateBetStatus(betCheck.get("bet_id").toString(), status, existingExtraData.toString()
                            , BigDecimal.valueOf(odds), providerTxId, transactionId, BigDecimal.valueOf(maxWin), balance.toString())) {
                    }
                }

            } else {
                logger.info("Loss transaction for ProviderTxId: " + providerTxId);
                existingExtraData.addProperty("note", "No payout due - amount is zero");
                transactionId = null;
                status = 3;
                odds = 0.0;
                betAmount = 0.0;
                String newBalance = walletService.profileBalance(userId).get("balance").toString();
                balance.addProperty("credit_balance", newBalance);
                if (!walletService.updateBetStatus(betCheck.get("bet_id").toString(), status, existingExtraData.toString()
                        , BigDecimal.valueOf(odds), providerTxId, transactionId, BigDecimal.valueOf(amount), balance.toString())) {
                }
            }
            if (isRisk) {
                long outboxId = walletService.createOutboxMessage(userId, "SPRIBE",
                        ProfileOutbox.MessageType.NOTIFICATIONS, messageOut);

                try {
                    JsonObject json = new JsonObject();
                    json.addProperty("campaign_id", outboxId);
                    json.addProperty("message_pages", 1);
                    json.addProperty("message", messageOut);
                    json.addProperty("sms-id", "QUICKSENDVERIFICATION");
                    json.addProperty("network_regex", 1);
                    json.addProperty("network", "SAFARICOM");
                    json.addProperty("alert_type", "TRANSACTIONAL");
                    json.addProperty("recipients", "**********");
                    json.addProperty("outbox_id", outboxId);
                    json.addProperty("short_code", basics.SenderId);
                    json.addProperty("gateway", 1);
                    json.addProperty("dlr_url", "");
                    json.addProperty("auth_token", "auth_token_api");
                    json.addProperty("date_created", Utilities.now("yyyy-MM-dd HH:mm:ss"));

                    String jsonString = json.toString();
                    Queue.publishMessage(jsonString,
                            "OUTBOX",
                            "OUTBOX",
                            "OUTBOX");
                } catch (Exception e) {
                    logger.error("Failed to sleep: " + e.getMessage());
                }
            } else {
                return transactionId;
            }

            
        } catch (Exception e) {
            logger.error("Error updating player balance", e);
            throw new RuntimeException("Failed to update player balance", e);
        }
        return null;
    }

    @WithSpan
    public int isDuplicateAction(String providerTxId, String userId, Instant startTime) {
        logger.info("Checking for duplicate transaction for Tid: " + providerTxId);
        Map<String, Object> existingBet = walletService.getDebitBet(userId, providerTxId);

        if (existingBet != null) {
            return 1;
        }
        return 0;
    }

    @WithSpan
    @Transactional
    public int isDuplicateTransaction(String providerTxId, String withdrawProviderTxId, String userId,
                                      Instant startTime, BigDecimal amount) {
        BigDecimal roundedAmount = amount.setScale(2, RoundingMode.HALF_UP);
        List<Map<String, Object>> recentBets = walletService.findCreditBet(providerTxId, userId);

        if (recentBets != null) {
            return 1;
        } else {
            return 0;
        }
    }

    @WithSpan
    public String processDebit(String profileId, String provider, String providerTxId, String action,
                               String actionId, BigDecimal amount, String description,
                               String game, JsonObject extraData) {
        String currentBalance = walletService.profileBalance(profileId).get("balance").toString();

        int betType = 0;
        if (action == "bonus" || action == "rain"){
            betType = 1;
        } else if (action == "freebet" || action == "rainfreebet" || action == "promofreebet" || action == "challengefreebet"){
            betType = 2;
        }
        logger.info("Action is : " + action + " so bet type is : " + betType);
        try {
            if (!walletService.debitProfileBalance(profileId, amount, BigDecimal.ZERO)) {
                return null; // Insufficient balance
            }
            logger.info("Updated balance for profileId: " + profileId);
            
            // Create transaction record
            logger.debug("Creating transaction for action Id: " + actionId);
            String transactionId = walletService.createTransaction(profileId, providerTxId, amount, game,
                    extraData.toString());
            if (transactionId == null) {
                return null;
            }
            Short status = 0;
            String newBalance = walletService.profileBalance(profileId).get("balance").toString();
            Map<String, Object> balance = new HashMap<>();
            balance.put("original_balance", currentBalance);
            balance.put("debit_balance", newBalance);
            // Create bet record
            logger.debug("Creating bet record for gameId : " + providerTxId);
            String betId = walletService.createBet(profileId, amount, providerTxId, (short) betType, provider,
                        status, transactionId, action, actionId, description, game, extraData.toString(), balance.toString());
            if (betId == null) {
                return null;
            }
            
            return transactionId;
            
        } catch (Exception e) {
            logger.error("Error processing debit", e);
            return null;
        }
    }


    /**
     * Processes rollback for a specific transaction ID
     *
     * @param rollbackTid The TID of the transaction to rollback
     * @param newTid The new transaction ID for the rollback
     * @param userId User ID
     * @param gameId Game ID
     * @param actionId Action ID
     * @param amount Amount to rollback
     * @param extraData Additional data for the rollback transaction
     * @return Transaction ID if successful, null otherwise
     */

    @WithSpan
    @Transactional
    public String processRollback(String userId, String providerTxId, String rollbackProviderTxId, String actionId,
                                  BigDecimal amount, JsonObject extraData) {
        String currentBalance = walletService.profileBalance(userId).get("balance").toString();
        try {
            // Find original transaction
            Map<String, Object> originalTransaction = walletService.rollbackBet(rollbackProviderTxId, userId);
            if (originalTransaction == null) {
                return null;
            }
            if(originalTransaction.get("bet_id") == null){
                // Determine rollback amount
                int originalType = (Integer) originalTransaction.get("transactionTypeId");
                BigDecimal originalAmount = (BigDecimal) originalTransaction.get("amount");
                boolean wasDebit = (originalType == TransactionConstants.TYPE_DEBIT);

                // Update player balance
                walletService.creditProfileBalance(userId, amount, BigDecimal.ZERO);

                JsonObject rollbackExtraDataBuilder = new JsonObject();
                // Merge extraData into rollbackExtraDataBuilder
                if (extraData != null) {
                    for (String key : extraData.keySet()) {
                        rollbackExtraDataBuilder.add(key, extraData.get(key));
                    }
                }
                rollbackExtraDataBuilder.addProperty("rollback_tid", providerTxId);
                rollbackExtraDataBuilder.addProperty("original_amount", originalAmount.toString());
                rollbackExtraDataBuilder.addProperty("rollback_type", "transaction_rollback");

                // Create rollback transaction
                String rollbackTransactionId = walletService.rollbackTransaction(userId, providerTxId, amount, rollbackProviderTxId,
                        rollbackExtraDataBuilder.toString(), wasDebit);
                if (rollbackTransactionId == null || rollbackTransactionId.trim().isEmpty()) {
                    logger.error("Failed to create rollback transaction for TID: " + rollbackProviderTxId);
                    return null;
                } else {
                    logger.info("Rollback transaction created successfully for TID: " + rollbackProviderTxId);
                    return rollbackTransactionId;
                }
            }
            if(originalTransaction.get("bet_id") != null){
                logger.info("Updating bet status to rolled back for bet ID: " + originalTransaction.get("bet_id"));
                JsonObject betExtraDataBuilder = new JsonObject();
                betExtraDataBuilder.addProperty("rollback_tid", rollbackProviderTxId);
                betExtraDataBuilder.addProperty("rollback_transaction_id",
                        originalTransaction.get("id").toString());
                betExtraDataBuilder.addProperty("rollback_date",
                        Utilities.GetUTCDate("yyyy-MM-dd HH:mm:ss"));
                betExtraDataBuilder.addProperty("rollback_type", providerTxId);

                String newBalance = walletService.profileBalance(userId).get("balance").toString();
                Map<String, Object> balance = new HashMap<>();
                balance.put("old_balance", currentBalance);
                balance.put("new_balance", newBalance);

                if(walletService.updateBetStatus(originalTransaction.get("bet_id").toString(), 3,
                        betExtraDataBuilder.toString(), null, providerTxId, null, null, balance.toString())){
                    logger.info("Bet status updated successfully for bet ID: " + originalTransaction.get("bet_id"));
                    return originalTransaction.get("id").toString();
                }
            }

        } catch (Exception e) {
            logger.error("Error processing rollback for TID: " + providerTxId, e);
            throw new RuntimeException("Rollback failed", e);

        }
        return null;
    }

    /**
     * Processes cancellation rollback (subtype="cancel")
     * This type of rollback always credits the player
     *
     * @param tid Transaction ID for the cancellation
     * @param userId User ID
     * @param gameId Game ID
     * @param actionId Action ID
     * @param amount Amount to credit back to player
     * @param extraData Additional data for the cancellation transaction
     * @return Transaction ID if successful, null otherwise
     */

//    @WithSpan
//    public String processCancellation(String type, String providerTxId, String userId,
//                                      String actionId, BigDecimal amount, JsonObject extraData) {
//
//        logger.info("Cancellation: " + type + ", Provider Tx ID: " + providerTxId + ", User ID: " + userId +
//                ", Action ID: " + actionId + ", Amount: " + amount +
//                ", Extra Data: " + extraData);
//
//        try {
//            // Update player balance
//            if (type.equals("debit")) {
//                if (!walletService.debitProfileBalance(userId, amount, BigDecimal.ZERO)) {
//                    logger.error("Failed to update balance for cancellation");
//                    return null;
//                }
//            } else {
//                if (!walletService.creditProfileBalance(userId, amount, BigDecimal.ZERO)) {
//                    logger.error("Failed to update balance for cancellation");
//                    return null;
//                }
//            }
//
//            JsonObject cancellationExtraDataBuilder = new JsonObject();
//            if (extraData != null) {
//                for (String key : extraData.keySet()) {
//                    cancellationExtraDataBuilder.add(key, extraData.get(key));
//                }
//            }
//            cancellationExtraDataBuilder.addProperty("cancellation_type", "game_server_cancel");
//            cancellationExtraDataBuilder.addProperty("subtype", "cancel");
//            cancellationExtraDataBuilder.addProperty("original_action_id", actionId);
//
//
//            Long transactionId;
//            transactionId = Long.valueOf(walletService.cancellationTransaction(userId, providerTxId, amount,
//                    actionId, cancellationExtraDataBuilder.toString()));
//
//            // Create cancellation transaction
//            if (transactionId == null) {
//                logger.error("Failed to create cancellation transaction for Provider: " + providerTxId);
//                return null;
//            }
//
//            // Find and update related bet
//            Map<String, Object> bet = softgamingBetRepository.findBetByExtradata(userId, gameId, actionId);
//
//            if (bet != null) {
//                int betId = (int) bet.get("bet_id");
//
//                int status = TransactionConstants.Status.CANCELLED;
//
//                JsonObject betExtraDataBuilder = new JsonObject();
//                betExtraDataBuilder.addProperty("cancellation_tid", tid);
//                betExtraDataBuilder.addProperty("cancellation_transaction_id", transactionId);
//                betExtraDataBuilder.addProperty("cancellation_date", Utilities.GetUTCDate("yyyy-MM-dd HH:mm:ss"));
//                betExtraDataBuilder.addProperty("cancellation_reason", "Game server cancellation");
//                betExtraDataBuilder.addProperty("cancellation_type", tid);
//
//                if (!softgamingBetRepository.updateCancelledBetStatus(String.valueOf(betId), status,
//                        betExtraDataBuilder.toString())) {
//                    logger.error("Failed to update bet status for cancellation");
//                    return null;
//                }
//            }
//
//            logger.info("Cancellation processed successfully for Provider Id: " + providerTxId +
//                       ", Action ID: " + actionId + ", Amount: " + amount);
//
//            return transactionId.toString();
//
//        } catch (Exception e) {
//            logger.error("Error processing cancellation for Provider Id: " + providerTxId, e);
//            throw new RuntimeException("Cancellation failed", e);
//        }
//    }

    /**
     * CalculateWitholdingTax
     *
     * @param amountWon
     * @param stakeAmount
     * @param taxValue
     * @param taxationType
     * @return
     */
    public static Map<String, Double> CalculateWitholdingTax(Double amountWon,
                                                             Double stakeAmount, Double taxValue, String taxationType) {

        Double possibleWin = (amountWon - stakeAmount);
        Double possibleWinAfter = (possibleWin - ((possibleWin * 100) / (100 + taxValue)));
        if (Utilities.containStr(new String[]{"DIRECT", "TYPE_1"}, taxationType.toUpperCase())) {
            possibleWinAfter = (possibleWin * (taxValue / 100));
        }

        Map<String, Double> whtax = new HashMap<>();
        whtax.put("posibleWin", (amountWon - possibleWinAfter));
        whtax.put("witholdingTax", possibleWinAfter);

        return whtax;
    }

    /**
     * CalculateExciseTax
     *
     * @param stakeAmount
     * @param taxValue
     * @param taxationType
     * @return
     */
    public static Map<String, Double> CalculateExciseTax(Double stakeAmount,
                                                         Double taxValue, String taxationType) {
        Double stakeTax = stakeAmount - ((stakeAmount * 100) / (100 + taxValue));
        if (Utilities.containStr(new String[]{"DIRECT"}, taxationType.toUpperCase())) {
            stakeTax = (stakeAmount * (taxValue / 100));
        }

        Map<String, Double> exciseTax = new HashMap<>();
        exciseTax.put("stakeTax", stakeTax);
        exciseTax.put("stakeAfterTax", (stakeAmount - stakeTax));

        return exciseTax;
    }
}
