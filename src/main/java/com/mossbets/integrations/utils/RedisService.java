package com.mossbets.integrations.utils;

import io.quarkus.redis.datasource.RedisDataSource;
import io.quarkus.redis.datasource.value.ValueCommands;
import io.vertx.core.impl.logging.LoggerFactory;
import jakarta.enterprise.context.ApplicationScoped;
import io.quarkus.redis.datasource.keys.KeyCommands;

import static io.netty.handler.codec.http.multipart.DiskAttribute.prefix;

@ApplicationScoped
public class RedisService {
    private static final io.vertx.core.impl.logging.Logger logger = LoggerFactory.getLogger(RedisService.class);

    private final ValueCommands<String, String> valueCommands;
    private final KeyCommands<String> keyCommands;

    public RedisService(RedisDataSource redisDataSource) {
        this.valueCommands = redisDataSource.value(String.class);
        this.keyCommands = redisDataSource.key();
    }

    public void add(String key, String value, int timeout) {
        keyCommands.del(key);
        logger.info("Adding redis record");
        if (timeout > 0) {
            valueCommands.setex(key, timeout, value);
        } else {
            valueCommands.set(key, value);
        }
    }

    public String get(String key) {
        return valueCommands.get(key);
    }

    public void update(String key, String value) {
        valueCommands.set(key, value);
    }

    public int delete(String key) {
        return keyCommands.del(key);
    }
}