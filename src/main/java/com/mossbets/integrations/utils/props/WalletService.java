package com.mossbets.integrations.utils.props;

import com.mossbets.integrations.entities.profile.ProfileOutbox;
import com.mossbets.integrations.repositories.*;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import io.quarkus.hibernate.orm.PersistenceUnit;
import io.vertx.core.impl.logging.LoggerFactory;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import jakarta.transaction.UserTransaction;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@ApplicationScoped
public class WalletService {
    private static final io.vertx.core.impl.logging.Logger logger = LoggerFactory.getLogger(WalletService.class);

    @Inject
    SpribeBetRepository spribeBetRepository;

    @Inject
    ProfileRepository profileRepository;

    @Inject
    ProfileLoginRepository profileLoginRepository;

    @Inject
    TransactionBetRepository transactionBetRepository;

    @Inject
    BetLimitRepository betLimitRepository;

    @Inject
    TransactionRepository transactionRepository;
    @Inject
    ProfileBalanceRepository profileBalanceRepository;

    @Inject
    TransactionPendingApprovalRepository transactionPendingApprovalRepository;

    @Inject
    TransactionLeaderboardStatsBlacklistRepository transactionLeaderboardStatsBlacklistRepository;

    @Inject
    ProfileOutboxRepository profileOutboxRepository;

    @Inject
    PromotionRepository promotionRepository;

    @Inject
    LeaderboardRepository leaderboardRepository;

    @Inject
    @PersistenceUnit("profile")
    EntityManager profileEntityManager;

    @Inject
    @PersistenceUnit("transactions")
    EntityManager transactionEntityManager;

    @Inject
    @PersistenceUnit("bets")
    EntityManager betsEntityManager;

    @Inject
    @PersistenceUnit("bonus")
    EntityManager pendingApprovalEntityManager;

    @Inject
    UserTransaction userTransaction;

    @WithSpan
    @Transactional
    public Map<String, Object> betLimit(int clientId, String betName ) {
        Map<String, Object> limits = betLimitRepository.getBetLimit(clientId, betName);
        return limits;
    }
    @WithSpan
    @Transactional
    public Map<String, Object> getDebitBet(String profileId, String providerTxId ) {
        Map<String, Object> betCheck = spribeBetRepository.findDebitBet(profileId, providerTxId);
        return betCheck;
    }
    @WithSpan
    @Transactional
    public String createTransaction(String userId, String providerTxId, BigDecimal amount, String game,
                                                 String existingExtraData) {
        String betCheck = transactionRepository.createTransaction(userId, providerTxId, amount, game,
        existingExtraData);
        return betCheck;
    }

    @WithSpan
    @Transactional
    public String createCreditTransaction(String userId, String providerTxId, BigDecimal amount, String game,
                                    String existingExtraData,String source, String description) {
        String betCheck = transactionRepository.createCreditTransaction(userId, providerTxId, amount, game,
                existingExtraData, source, description);
        return betCheck;
    }
    @WithSpan
    @Transactional
    public Map<String, Object> profileBalance(String userId){
        Map<String, Object> balance = profileBalanceRepository.getPlayerBalance(userId);
        return balance;
    }
    @WithSpan
    @Transactional
    public boolean createPendingApproval(String profileId, String transactionId, BigDecimal amount, String messageOut) {
        return transactionPendingApprovalRepository.insertPendingApproval(profileId, transactionId,
                amount, messageOut);
    }
    @WithSpan
    @Transactional(Transactional.TxType.REQUIRES_NEW)
    public boolean creditProfileBalance(String profileId, BigDecimal amount, BigDecimal bonusAmount) {
        try {
            return profileBalanceRepository.creditBalance(profileId, amount, bonusAmount);
        } catch (Exception e) {
            logger.error("Error in creditProfileBalance", e);
            return false;
        }
    }
    @WithSpan
    @Transactional
    public boolean updateBetStatus(String betId, int status, String existingExtraData, BigDecimal odds, String providerTxId,
            String transactionId, BigDecimal amount, String balance) {
        return spribeBetRepository.updateBetStatus(betId, status, existingExtraData,
                odds, providerTxId, transactionId, amount, balance);
    }
    @WithSpan
    @Transactional
    public long createOutboxMessage(String profileId, String senderId, ProfileOutbox.MessageType messageType,
                                    String message) {
        long outboxId = profileOutboxRepository.insertOutboxMessage(profileId, senderId,
                messageType, message);
        return outboxId;
    }
    @WithSpan
    @Transactional
    public boolean debitProfileBalance(String profileId, BigDecimal amount, BigDecimal bonus) {
        return profileBalanceRepository.debitBalance(profileId, amount, bonus);
    }

    @WithSpan
    @Transactional
    public String createBet(String profileId, BigDecimal amount,
                             String providerTxId, short betType, String providerName,
                            Short status, String transactionId, String action, String actionId,
                             String Desc, String game, String extraData, String balance) {
        String bet = spribeBetRepository.createBet(profileId, amount, providerTxId, betType, providerName,
                status, transactionId, actionId, Desc, game, extraData, balance);
        return bet;
    }
    @WithSpan
    @Transactional
    public boolean checkBlacklist(String userId){
        return transactionLeaderboardStatsBlacklistRepository.findByProfileIdAndStatus(userId).isEmpty();
    }
    @WithSpan
    @Transactional
    public Optional<Map<String, Object>> getCampaign(){
        return promotionRepository.getActiveLeaderboardCampaign();
    }

    @WithSpan
    @Transactional
    public boolean createLeaderboard(Integer promoId,  String profileID, long betCount, BigDecimal amount, BigDecimal odds,
                                     Short status, LocalDate promoDate){
        return leaderboardRepository.upsertLeaderboardStats(promoId, profileID, betCount, amount, odds, status, promoDate);
    }
    @WithSpan
    @Transactional(Transactional.TxType.REQUIRES_NEW)
    public  Map<String, Object> rollbackBet(String rollbackProviderTxId, String userId){
        Map<String, Object> bet = transactionBetRepository.findTransactionByTid(rollbackProviderTxId, userId);
        return bet;
    }
    @WithSpan
    @Transactional
    public boolean getPendingApproval(String profileId){
        return profileLoginRepository.hasActiveLogin(profileId);
    }
    @WithSpan
    @Transactional
    public Map<String, Object> playerDetails(String profileId){
        return profileRepository.getPlayerDetails(profileId);
    }
    @WithSpan
    @Transactional(Transactional.TxType.REQUIRES_NEW)
    public String rollbackTransaction(String userId, String newTid,  BigDecimal amount, String rollbackTid, String extraData,
                                      boolean wasDebit) {
        String rollbackTransactionId = transactionRepository.createRollbackTransaction(userId, newTid, amount, rollbackTid,
                extraData, wasDebit);
        return rollbackTransactionId;
    }
    @WithSpan
    @Transactional
    public String cancellationTransaction(String userId, String tid, BigDecimal amount, String actionId, String extraData) {
        String transactionId = transactionRepository.createCancellationTransaction(userId, tid, amount,
                actionId, extraData);
        return transactionId;
    }
    @WithSpan
    @Transactional
    public Map<String, Object> getLeaderboardStats(int promoId, String profileId) {
        Map<String, Object> existingStats = leaderboardRepository
                .findByPromoAndProfile(promoId, profileId);
        logger.info("Existing stats: " + existingStats);
        return existingStats;
    }
    @WithSpan
    @Transactional
    public List<Map<String, Object>> findCreditBet(String providerTxId, String profileId) {
        return spribeBetRepository.findCreditBet(profileId, providerTxId);
    }

}
