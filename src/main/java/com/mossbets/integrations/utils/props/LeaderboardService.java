package com.mossbets.integrations.utils.props;

import com.mossbets.integrations.repositories.LeaderboardRepository;
import com.mossbets.integrations.repositories.PromotionRepository;
import com.mossbets.integrations.utils.RedisService;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import io.quarkus.hibernate.orm.PersistenceUnit;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import org.jboss.logging.Logger;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;

@ApplicationScoped
public class LeaderboardService {

    @Inject
    RedisService redisService;

    @Inject
    LeaderboardRepository leaderboardRepository;

    @Inject
    @PersistenceUnit("transactions")
    EntityManager entityManager;

    @Inject
    WalletService walletService;

    @Inject
    public Logger logger;

    @Inject
    PromotionRepository promotionRepository;

    private static final Logger LOG = Logger.getLogger(LeaderboardService.class);
    @WithSpan
    public Optional<Map<String, Object>> getLeaderboardCampaign(Integer campaignPromoId) {
        String key = "LeaderboardService:GetLeaderboardCampaign";
        if (campaignPromoId != null) {
            key += ":PromoId:" + campaignPromoId;
        }

        String redisData = redisService.get(key);
        logger.info("Redis data: " + redisData);
        Optional<Map<String, Object>> dataObject = parseStringToMap(redisData);
        if (!isOptionalEmpty(String.valueOf(dataObject))) {
            return dataObject;
        } else {
            Optional<Map<String, Object>> result = walletService.getCampaign();
            if (result.isPresent()) {
                String finalKey = key;
                logger.info("Updating Redis with :" + result + " key : " + key);
                redisService.add(finalKey, result.get().toString(), 3600);
            }
            return result;
        }
    }

    @WithSpan
    @Transactional
    public boolean createLeaderboardEntry(String profileId, double betAmount) {
        Optional<Map<String, Object>> promoOpt = getLeaderboardCampaign(null);
        if (promoOpt.isEmpty()) return false;

        Map<String, Object> promo = promoOpt.get();
        double minStake = ((Number) promo.get("min_stake")).doubleValue();
        if (betAmount < minStake) return false;

        if (!walletService.checkBlacklist(profileId)) {
            return false;
        }

        return walletService.createLeaderboard(
                (Integer) promo.get("id"),
                profileId,
                1L,
                BigDecimal.valueOf(betAmount),
                BigDecimal.ZERO,
                (short) 1,
                LocalDate.now()
        );
    }
    @WithSpan
    @Transactional
    public boolean updateOdds(String profileId, double odds, double betAmount) {
        Optional<Map<String, Object>> promoOpt = getLeaderboardCampaign(null);
        if (promoOpt.isEmpty()) {
            return false;
        }

        Map<String, Object> promo = promoOpt.get();
        double minStake = ((Number) promo.get("min_stake")).doubleValue();
        double minOdds = ((Number) promo.get("min_odds")).doubleValue();
        if (betAmount < minStake) return false;
        if (odds < minOdds) return false;

        Map<String, Object> existingStats = leaderboardRepository
                .findByPromoAndProfile((Integer) promo.get("id"), profileId);

        if (existingStats != null) {
            double existingOdds = ((Number) existingStats.get("odds")).doubleValue();
            Long id = (Long) existingStats.get("id");
            Short status = (Short) existingStats.get("status");
            if (status == 0) {
                return false;
            }
            if (existingOdds >= odds) {
                return false;
            } else {
                logger.info("Updating leaderboard entry with odds: " + odds);
                return leaderboardRepository.updateOdds(profileId, odds, id);
            }
        }

        return false;
    }
    public static double roundDoubleForDatabase(double value) {
        try {
            BigDecimal bd = BigDecimal.valueOf(value);
            bd = bd.setScale(2, RoundingMode.HALF_UP);
            return bd.doubleValue();
        } catch (Exception e) {
            return 0.0;
        }
    }
    public static Optional<Map<String, Object>> parseStringToMap(String input) {
        if (input == null || input.trim().isEmpty() || input.trim().equals("{}")) {
            return Optional.empty();
        }

        Map<String, Object> map = new HashMap<>();

        // Remove enclosing braces
        String content = input.trim();
        if (content.startsWith("{") && content.endsWith("}")) {
            content = content.substring(1, content.length() - 1);
        }

        // Split on commas not inside URLs or unescaped values
        String[] pairs = content.split(", (?=[^=]+=)");

        for (String pair : pairs) {
            String[] kv = pair.split("=", 2);
            if (kv.length == 2) {
                String key = kv[0].trim();
                String value = kv[1].trim();

                Object parsedValue;

                if ("null".equalsIgnoreCase(value)) {
                    parsedValue = null;
                } else if (value.matches("\\d+")) {
                    parsedValue = Integer.parseInt(value);
                } else if (value.matches("\\d+\\.\\d+")) {
                    parsedValue = Double.parseDouble(value);
                } else {
                    parsedValue = value;
                }

                map.put(key, parsedValue);
            }
        }
        return Optional.of(map);
    }
    public static boolean isOptionalEmpty(String input) {
        if (input == null || !input.startsWith("Optional[") || !input.endsWith("]")) {
            return true; // Treat invalid format as empty
        }

        // Extract inner content
        String inner = input.substring("Optional[".length(), input.length() - 1).trim();

        // Check if it's empty or just empty map notation
        return inner.isEmpty() || "{}".equals(inner);
    }
}