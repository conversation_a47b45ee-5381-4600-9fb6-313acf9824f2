package com.mossbets.integrations.utils.props;

import io.smallrye.config.ConfigMapping;
import io.smallrye.config.WithDefault;
import jakarta.enterprise.context.ApplicationScoped;

@ConfigMapping(prefix = "mbs")
@ApplicationScoped
public interface Props {

    /**
     * RabbitMQ Configs
     *
     * @return
     */
    @WithDefault("5672")
    int rabbitMqPort();

    @WithDefault("10000")
    int rabbitMqTimeout();

    @WithDefault("10000")
    int rabbitMqHandShakeTimeout();

    @WithDefault("45")
    int rabbitMqHeartbeat();

    @WithDefault("1000")
    int rabbitMqNetworkRecovery();

    @WithDefault("200")
    int rabbitMqPrefetchCount();

    @WithDefault("5000")
    int rabbitMqConnectionSleepTime();

    /**
     * RabbitMq Host
     *
     * @return
     */
    String rabbitMqHost();

    /**
     * RabbitMq user
     *
     * @return
     */
    String rabbitMqUsername();

    /**
     * RabbitMq Pass
     *
     * @return
     */
    String rabbitMqPassword();

    @WithDefault("MOSSBETS")
    String rabbitMqPrefix();

    @WithDefault("/")
    String rabbitMqVhost();

    @WithDefault("true")
    boolean rabbitMqConRecovery();

    @WithDefault("true")
    boolean rabbitMqTopologyRecovery();

    /**
     * Redis Configs
     *
     * @return
     */
    @WithDefault("6379")
    int redisPort();

    @WithDefault("2")
    int redisMinIdle();

    @WithDefault("2")
    int redisMaxIdle();

    @WithDefault("3")
    int redisMaxWait();

    @WithDefault("3540")
    int redisTimeOut();

    String redisHost();

    String redisUser();
    

    String redisAuth();

    @WithDefault("B2B")
    String redisStr();

    @WithDefault("100")
    int numberOfThreads();

    @WithDefault("20000")
    int connectTimeOut();

    @WithDefault("30000")
    int socketTimeOut();

    String serverName();

    String launchUrl();

    String cipherUrl();

    String encryptionKey();

    String softSecret();

    String softApi();

    String softApikey();

    String softApipass();

    String softCurrency();

    int softStake();

    int softPayout();

    Integer softVoid();

    int softRefunds();

    int softClientId();


    String SpribeGameLaunch();
    String SpribeDemo();
    String ReturnUrl();
    String ReturnUrlDev();
    String SpribeOperatorKey();
}
