package com.mossbets.integrations.controllers;
import java.util.*;

import com.mossbets.integrations.controllers.resources.Configurations;
import com.mossbets.integrations.utils.Utilities;

import com.mossbets.integrations.utils.WalletUtils;
import com.mossbets.integrations.utils.crypto.HmacService;
import com.mossbets.integrations.utils.props.LeaderboardService;
import com.mossbets.integrations.utils.props.Props;

import io.opentelemetry.instrumentation.annotations.WithSpan;
import io.vertx.core.impl.logging.LoggerFactory;
import jakarta.enterprise.context.ApplicationScoped;

import jakarta.inject.Inject;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.HttpHeaders;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import com.google.gson.JsonObject;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import jakarta.json.Json;
@Path("/spribe/v1/")
@Produces(MediaType.APPLICATION_JSON)
@ApplicationScoped
public class ApiResource extends Configurations{

    private static final io.vertx.core.impl.logging.Logger logger = LoggerFactory.getLogger(ApiResource.class);
    @Inject
    private WalletUtils WalletUtils;
    @Inject
    private Props props;
    @Inject
    HmacService hmacService;
    @Inject
    LeaderboardService leaderboardService;

    @Inject
    Utilities utilities;

    @POST
    @Path("launch")
    @Consumes(MediaType.APPLICATION_JSON)
    @WithSpan
       public Response gameLaunch(@Context HttpHeaders headers, String rawRequest) throws Exception {
        Instant startTime = Instant.now();
        String appKey = headers.getHeaderString("x-app-key");
        String accessKey = headers.getHeaderString("x-hash-key");

        if (appKey == null) {
            return Response.status(Response.Status.BAD_REQUEST).entity("Missing header").build();
        }
        
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, Object> requestData = objectMapper.readValue(rawRequest, Map.class);
        int mode = Optional.ofNullable(requestData.get("mode")).map(val -> (val instanceof Number) ? ((Number) val).intValue() : Integer.parseInt(val.toString())).orElse(0);
        
        if (mode == 1 && accessKey == null) {
            return Response.status(Response.Status.BAD_REQUEST).entity("Missing header").build();
        }
        String game = Optional.ofNullable(requestData.get("game_id")).map(Object::toString).orElse(null);
        
        Map<String, Object> authResults = null;

        try {

            String token = "";
            String user = "";
            String currency = props.softCurrency();
            String operator = "";


            if (mode == 1) {
                    authResults = utilities.authenticateAccessKey(accessKey);
                    if ((int) authResults.get("code") != 200) {
                        return Response.status((int) authResults.get("code")).entity((String) authResults.get("message")).build();
                    }
                Map<String, Object> data = (Map<String, Object>) authResults.get("data");
                token = Utilities.encrypt(data.get("profile_id").toString(), true, props.softSecret());
                user = data.get("profile_id").toString();
                operator = "mossbets";
            }

            // Optional parameters
            String lang = "en";
            String returnUrl = mode == 1 ? props.ReturnUrl():props.ReturnUrlDev();

            String launchUrl = buildLaunchUrl(game, user, token, currency, operator,lang, returnUrl,mode == 0);
            
            jakarta.json.JsonObject response = Json.createObjectBuilder()
                .add("code", 200)
                .add("message", "Game launch successful")
                .add("url", launchUrl)
                .build();
            
            return Response.ok(response.toString()).build();
            
        } catch (Exception ex) {
            logger.error("Game launch error: " + ex.getMessage());
            return sendError(500, "Internal Server Error", startTime);
        }
    }
    private String buildLaunchUrl(String game, String user, String token, String currency, String operator,
                                 String lang, String returnUrl,boolean isDemo) {
        String baseUrl = isDemo ? props.SpribeDemo() : props.SpribeGameLaunch();
        
        StringBuilder url = new StringBuilder(baseUrl).append(game);
        List<String> params = new ArrayList<>();
        params.add("currency=" + URLEncoder.encode(currency, StandardCharsets.UTF_8));
        if(user != null && !user.isEmpty()){params.add("user=" + URLEncoder.encode(user, StandardCharsets.UTF_8));};
        if(token != null && !token.isEmpty()){params.add("token=" + URLEncoder.encode(token, StandardCharsets.UTF_8));};
        if(operator != null && !operator.isEmpty()){params.add("operator=" + URLEncoder.encode(operator, StandardCharsets.UTF_8));};
        if(lang != null){params.add("lang=" + URLEncoder.encode(lang, StandardCharsets.UTF_8));};
        if(returnUrl != null){params.add("return_url=" + URLEncoder.encode(returnUrl, StandardCharsets.UTF_8));};
        url.append("?").append(String.join("&", params));
        return url.toString();
    }


    @POST
    @Path("info")
    @Consumes(MediaType.APPLICATION_JSON)
    @WithSpan
    public Response getBalance(@Context HttpHeaders headers, String rawRequest) throws Exception {
        Instant startTime = Instant.now();
        logger.info("Initiating transaction with request: " + rawRequest.toString());
        String clientID = headers.getHeaderString("X-Spribe-Client-ID");
        String clientTs = headers.getHeaderString("X-Spribe-Client-TS");
        String clientSignature = headers.getHeaderString("X-Spribe-Client-Signature");

        JsonObject request = null;
        try {
            request = hmacService.stringToJsonObject(rawRequest);
        } catch (Exception e) {
            logger.error(Utilities.getLogPreString("InitiateAPI")
                    + "Initialize()"
                    + "|Failed to parse JSON: " + e.getMessage());
            return sendError(403,"Invalid parameters", startTime);
        }

        try {
            if ( !request.has("user_id") || !request.has("currency") ||
                    !request.has("session_token")) {
                return sendError(403,"Invalid parameters", startTime);
            }
            String requestCurrency = request.get("currency").getAsString();
            if (!props.softCurrency().equals(requestCurrency)) {
                return sendError(403,"Invalid currency", startTime);
            }

            String userId = request.get("user_id").getAsString();

            Map<String, Object> balanceInfo2 = WalletUtils.getPlayerBalance(userId);
            String balance = balanceInfo2.get("balance").toString();
            String userName = WalletUtils.getPlayerDetails(userId).get("name").toString();

            Map<String, Object> data = new HashMap<>();
            data.put("new_balance", balance);
            data.put("user_id", userId);
            data.put("currency", props.softCurrency());
            data.put("username", userName);

            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", "ok");
            response.put("data", data);
            logger.info(Utilities.getLogPreString("BalanceAPI")
                    + "Request successful. User: " + userId
                        + " | response: " + response.toString()
                    + "Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");
            return Response.ok(response.toString()).type(MediaType.APPLICATION_JSON).build();

        } catch (Exception e) {
            logger.error(Utilities.getLogPreString("BalanceAPI")
                    + "Took " + Utilities.CalculateTAT(startTime) + " mSec(s)"
                    + " | Exception: ", e);
            return sendError(403,"Request failed", startTime);
        }
    }

    @WithSpan
    @POST
    @Path("withdraw")
    @Consumes(MediaType.APPLICATION_JSON)
    public Response debit(@Context HttpHeaders headers, String rawRequest) throws Exception {
        logger.info("Initiating transaction with request: " + rawRequest.toString());
        Instant startTime = Instant.now();
        String clientID = headers.getHeaderString("X-Spribe-Client-ID");
        String clientTs = headers.getHeaderString("X-Spribe-Client-TS");
        String clientSignature = headers.getHeaderString("X-Spribe-Client-Signature");
        JsonObject request = null;
        try {
            request = hmacService.stringToJsonObject(rawRequest);
        } catch (Exception e) {
            logger.error(Utilities.getLogPreString("InitiateAPI")
                    + "Initialize()"
                    + "|Failed to parse JSON: " + e.getMessage());
            return sendError(403,"Invalid parameters", startTime);
        }
        String userId = request.get("user_id").getAsString();
        String stringBalance = WalletUtils.getPlayerBalance(userId).get("balance").toString();
        double dbBalance = Double.parseDouble(stringBalance);

        try {
            if (!request.has("user_id") || !request.has("currency") ||
                    !request.has("amount") || !request.has("provider") ||
                    !request.has("provider_tx_id") || !request.has("game") ||
                    !request.has("action") || !request.has("action_id") ||
                    !request.has("session_token") || !request.has("platform")) {
                return sendError(403,"Invalid parameters", startTime);
            }

            String currency = request.get("currency").getAsString();
            String amountStr = request.get("amount").getAsString();
            String provider = request.get("provider").getAsString();
            String providerTxId = request.get("provider_tx_id").getAsString();
            String actionId = request.get("action_id").getAsString();
            String game = request.get("game").getAsString();
            String action = request.get("action").getAsString();
            String platform = request.get("platform").getAsString();
            String token = request.get("session_token").getAsString();


            if (!props.softCurrency().equals(currency)) {
                logger.info(Utilities.getLogPreString("Debit")
                        + "Request failed - Invalid currency. User: " + userId + " Provider Id" + providerTxId
                        + " | Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");
                return sendError(403,"Invalid currency", startTime);
            }
            if (!WalletUtils.getInfo(userId)){
                logger.info(Utilities.getLogPreString("Debit")
                        + "Request failed - Invalid user. User: " + userId + " Provider Id" + providerTxId
                        + " | Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");
                return sendError(403,"Invalid userid", startTime);
            }

            BigDecimal amount;
            try {
                amount = new BigDecimal(amountStr);
                if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                    logger.info(Utilities.getLogPreString("Debit")
                            + "Request failed. User: " + userId + " Provider Id" + providerTxId
                            + " | Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");
                    return sendError(403,"Invalid parameters", startTime);
                }
            } catch (NumberFormatException e) {
                logger.info(Utilities.getLogPreString("Debit")
                        + "Request failed. User: " + userId + " Provider Id" + providerTxId
                        + " | Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");
                return sendError(403,"Invalid parameters", startTime);
            }
            // Check for rollback scenarios in debit
            boolean isCancellation = request.has("subtype") && "cancel".equals(request.get("subtype").getAsString());

            String transactionId = null;

            Map<String, Object> balanceInfo = WalletUtils.getPlayerBalance(userId);
            String currentBalance = balanceInfo.get("balance").toString();

            int duplicate = WalletUtils.isDuplicateAction(providerTxId, userId, startTime);

            if(duplicate==1){
                    logger.info("Duplicate transaction detected for Tid: " + providerTxId + ", Action ID: " + actionId);
                    return duplicateError(userId,"dpl" + providerTxId, provider, providerTxId, dbBalance, startTime);
            }

            BigDecimal currentBalanceBD = new BigDecimal(currentBalance);
            if (currentBalanceBD.compareTo(amount) < 0) {
                logger.info(Utilities.getLogPreString("Debit")
                        + "Request failed - Insufficient funds. User: " + userId + " Provider Id" + providerTxId
                        + " | Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");
                return sendError( 401,"INSUFFICIENT_FUNDS", startTime);
            }
            request.addProperty("ip_address", Utilities.getClientIpAddress());
            transactionId = WalletUtils.processDebit(userId, provider, providerTxId, action, actionId,
                            amount, "Debit for game: " + provider, game, request);
            if(leaderboardService.createLeaderboardEntry(userId, amount.doubleValue())){
                logger.info("Successfully created or updated leaderboard entry for user: " + userId);
            }
            if (transactionId == null) {
                logger.info(Utilities.getLogPreString("Debit")
                        + "Request failed. User: " + userId + " Provider Id" + providerTxId
                        + " | Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");
                return sendError( 400,"Transaction failed", startTime);
            }
            return successResponse( userId, providerTxId, provider, providerTxId, dbBalance, startTime);
        } catch (Exception e) {
            logger.info(Utilities.getLogPreString("Debit")
                    + "Request failed. User: " + userId
                    + " | Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");
            return sendError( 400, "Transaction failed", startTime);
        }
    }

    @WithSpan
    @POST
    @Path("deposit")
    @Consumes(MediaType.APPLICATION_JSON)
    public Response credit(@Context HttpHeaders headers, String rawRequest) throws Exception {
        logger.info("Initiating transaction with request: " + rawRequest.toString());
        Instant startTime = Instant.now();
        String clientID = headers.getHeaderString("X-Spribe-Client-ID");
        String clientTs = headers.getHeaderString("X-Spribe-Client-TS");
        String clientSignature = headers.getHeaderString("X-Spribe-Client-Signature");

        JsonObject request = null;
        try {
            request = hmacService.stringToJsonObject(rawRequest);
        } catch (Exception e) {
            logger.error(Utilities.getLogPreString("InitiateAPI")
                    + "Initialize()"
                    + "|Failed to parse JSON: " + e.getMessage());
            return sendError(403,"Invalid parameters", startTime);
        }

        String userId = request.get("user_id").getAsString();
        Map<String, Object> balanceInfo2 = WalletUtils.getPlayerBalance(userId);
        String balance = balanceInfo2.get("balance").toString();
        try {
            // Validate required fields
            if (!request.has("session_token") || !request.has("amount") || !request.has("game") ||
                    !request.has("user_id") || !request.has("action_id") || !request.has("action") ||
                    !request.has("provider") || !request.has("provider_tx_id") ||
                    !request.has("currency")) {
                logger.info(Utilities.getLogPreString("Credit")
                        + "Request failed - Missing parameters. User: " + userId
                        + " | Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");
                return sendError(403,"Invalid parameters", startTime);
            }

            String currency = request.get("currency").getAsString();
            String amountStr = request.get("amount").getAsString();
            String provider = request.get("provider").getAsString();
            String providerTxId = request.get("provider_tx_id").getAsString();
            String actionId = request.get("action_id").getAsString();
            String game = request.get("game").getAsString();
            String action = request.get("action").getAsString();
            String platform = request.get("platform").getAsString();
            String token = request.get("session_token").getAsString();
            String withdraw_provider_tx_id = request.get("withdraw_provider_tx_id").getAsString();

            if (!props.softCurrency().equals(currency)) {
                logger.info(Utilities.getLogPreString("Credit")
                        + "Request failed - Invalid currency. User: " + userId + " Provider Id" + providerTxId
                        + " | Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");
                return sendError(403,"Invalid currency", startTime);
            }

            if (!WalletUtils.getInfo(userId)){
                logger.info(Utilities.getLogPreString("Credit")
                        + "Request failed - Invalid user. User: " + userId + " Provider Id" + providerTxId
                        + " | Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");
                return sendError(403,"Invalid userid", startTime);
            }
            BigDecimal amount;
            try {
                amount = new BigDecimal(amountStr);
                if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                    logger.info(Utilities.getLogPreString("Credit")
                            + "Request failed - Invalid amount. User: " + userId + " Provider Id" + providerTxId
                            + " | Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");
                    return sendError(403, "Invalid parameters", startTime);
                }
            } catch (NumberFormatException e) {
                logger.info(Utilities.getLogPreString("Credit")
                        + "Request failed - Invalid amount. User: " + userId + " Provider Id" + providerTxId
                        + " | Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");
                return sendError(403,"Invalid parameters", startTime);
            }


                // Check for rollback scenarios
            boolean isRollback = request.has("i_rollback");
            boolean isPromotion = request.has("subtype") && "promotion".equals(request.get("subtype").getAsString());;
            boolean isCancellation = request.has("subtype") && "cancel".equals(request.get("subtype").getAsString());

            String transactionId = null;

            // Regular credit processing
            int duplicate = WalletUtils.isDuplicateTransaction(providerTxId,withdraw_provider_tx_id, userId, startTime, amount);

            if(duplicate==1){
                logger.info(Utilities.getLogPreString("Credit")
                        + "Request failed - Duplicate transaction. User: " + userId + " Provider Id" + providerTxId
                        + " | Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");
                return duplicateError(userId,"dpl" + providerTxId, provider, providerTxId, Double.parseDouble(balance), startTime);
            }
            request.addProperty("ip_address", Utilities.getClientIpAddress());

            // Process the credit
            transactionId = WalletUtils.updatePlayerBalance(
                        userId,
                        provider,
                        providerTxId,
                        action,
                        actionId,
                        amount.doubleValue(),
                        game,
                        request,
                        withdraw_provider_tx_id,
                        String.valueOf(System.currentTimeMillis())
            );

            if (transactionId == null) {
                logger.info(Utilities.getLogPreString("Credit")
                        + "Request failed. User: " + userId + " Provider Id" + providerTxId
                        + " | Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");
                return sendError(403,  "Transaction failed", startTime);
            }
            logger.info(Utilities.getLogPreString("Credit")
                        + "Request successful. User: " + userId + " Provider Id" + providerTxId
                        + " | Balance: " + balance
                        + " | Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");
                return successResponse(userId, transactionId, provider, providerTxId, Double.parseDouble(balance), startTime);

        } catch (Exception e) {
            logger.error("Error processing Credit request", e);
            return sendError(403,  "Transaction failed", startTime);
        }
    }

    @WithSpan
    @POST
    @Path("rollback")
    @Consumes(MediaType.APPLICATION_JSON)
    public Response rollback(@Context HttpHeaders headers, String rawRequest) throws Exception {
        Instant startTime = Instant.now();
        String clientID = headers.getHeaderString("X-Spribe-Client-ID");
        String clientTs = headers.getHeaderString("X-Spribe-Client-TS");
        String clientSignature = headers.getHeaderString("X-Spribe-Client-Signature");

        JsonObject request = null;
        try {
            request = hmacService.stringToJsonObject(rawRequest);
        } catch (Exception e) {
            logger.error(Utilities.getLogPreString("InitiateAPI")
                    + "Initialize()"
                    + "|Failed to parse JSON: " + e.getMessage());
            return sendError(403,"Invalid parameters", startTime);
        }
        String userId = request.get("user_id").getAsString();
        String stringBalance = WalletUtils.getPlayerBalance(userId).get("balance").toString();
        double dbBalance = Double.parseDouble(stringBalance);

        try {
            if (!request.has("user_id")  ||  !request.has("amount")
                    || !request.has("provider_tx_id") || !request.has("game")
                    || !request.has("action") || !request.has("action_id")
                    || !request.has("session_token") ) {
                return sendError(403,"Invalid parameters", startTime);
            }

            String amountStr = request.get("amount").getAsString();
            String providerTxId = request.get("provider_tx_id").getAsString();
            String actionId = request.get("action_id").getAsString();
            String game = request.get("game").getAsString();
            String provider = request.get("provider").getAsString();
            String action = request.get("action").getAsString();
            String rollbackProviderTxId = request.get("rollback_provider_tx_id").getAsString();
            String token = request.get("session_token").getAsString();

            BigDecimal amount;
            try {
                amount = new BigDecimal(amountStr);
                if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                    logger.info(Utilities.getLogPreString("Debit")
                            + "Request failed. User: " + userId + " Provider Id" + providerTxId
                            + " | Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");
                    return sendError(403,"Invalid parameters", startTime);
                }
            } catch (NumberFormatException e) {
                logger.info(Utilities.getLogPreString("Debit")
                        + "Request failed. User: " + userId + " Provider Id" + providerTxId
                        + " | Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");
                return sendError(403,"Invalid parameters", startTime);
            }

            String transactionId = null;
            request.addProperty("ip_address", Utilities.getClientIpAddress());
            transactionId = WalletUtils.processRollback(userId, providerTxId, rollbackProviderTxId, actionId,
                    amount, request);
            if (transactionId == null) {
                logger.info(Utilities.getLogPreString("Debit")
                        + "Request failed. User: " + userId + " Provider Id" + providerTxId
                        + " | Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");
                return sendError( 400,"Transaction failed", startTime);
            }
            return successResponse( userId, providerTxId, provider, providerTxId, dbBalance, startTime);
        } catch (Exception e) {
            logger.info(Utilities.getLogPreString("Debit")
                    + "Request failed. User: " + userId
                    + " | Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");
            return sendError( 400, "Transaction failed", startTime);
        }
    }

    @WithSpan
    public Response handlePing(@Context HttpHeaders headers, JsonObject request) {
        Instant startTime = Instant.now();
        String clientID = headers.getHeaderString("X-Spribe-Client-ID");
        String clientTs = headers.getHeaderString("X-Spribe-Client-TS");
        String clientSignature = headers.getHeaderString("X-Spribe-Client-Signature");
        try {
            if (!request.has("type") || !request.has("hmac")) {
                return sendError(403,  "Missing required fields (type or hmac)", startTime);
            }
            String requestType = request.get("type").getAsString();
            if (!"ping".equals(requestType)) {
                return sendError(403, "Invalid type. Expected 'ping'.", startTime);
            }
            String receivedHmac = request.get("hmac").getAsString();

            String calculatedHmac = hmacService.generateFundistHmac(hmacService.jsonObjectToMap(request), props.softSecret());
            if (!hmacService.validateHmac(props.softSecret(), request)) {
                logger.warn(Utilities.getLogPreString("HMAC_VALIDATION")
                        + "getBalance()"
                        + "|HMAC_Mismatch - Received: " + receivedHmac + ", Calculated: " + calculatedHmac);
                return sendError(403, "Invalid HMAC signature", startTime);
            }

            // Create a JSON builder and build the response
            JsonObject response = new JsonObject();
            response.addProperty("status", "OK");
            Map<String, Object> message = new HashMap<>();
            message.put("status", "OK");

            String responseHmac = hmacService.generateFundistHmac(message, props.softSecret());
            response.addProperty("hmac", responseHmac);
            logger.info(Utilities.getLogPreString("PingAPI")
                    + "Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");
            return Response.ok(response.toString()).build();

        } catch (Exception e) {
                logger.error(Utilities.getLogPreString("PingAPI")
                        + "Took " + Utilities.CalculateTAT(startTime) + " mSec(s)"
                        + "| Exception:", e);
                return sendError(403, "Internal Server Error", startTime);
        }
    }

    private Response sendError(int errorCode, String message, Instant startTime) {
        JsonObject errorResponse = new JsonObject();
        errorResponse.addProperty("code", errorCode);
        errorResponse.addProperty("message", message);

        logger.error(Utilities.getLogPreString("APIError")
                + " | Code: " + errorCode
                + " | Message: " + message
                + " | Took " + Utilities.CalculateTAT(startTime) + " mSec(s)");

        return Response.ok(errorResponse.toString())
                .type(MediaType.APPLICATION_JSON)
                .build();
    }
    public Response duplicateError(String userId, String operator_tx_id, String provider, String provider_tx_id,
                                   double balance, Instant startTime) {

        Map<String, Object> balanceInfo = WalletUtils.getPlayerBalance(userId);
        String newBalance = balanceInfo.get("balance").toString();

        Map<String, Object> data = new HashMap<>();
        data.put("operator_tx_id", operator_tx_id);
        data.put("new_balance", newBalance);
        data.put("old_balance", balance);
        data.put("user_id", userId);
        data.put("currency", props.softCurrency());
        data.put("provider", provider);
        data.put("provider_tx_id", provider_tx_id);

        Map<String, Object> response = new HashMap<>();
        response.put("code", 409);
        response.put("message", props.softCurrency());
        response.put("data", data);

        return Response.ok(response.toString()).type(MediaType.APPLICATION_JSON)
                .build();
        }
    public Response successResponse(String userId, String operator_tx_id, String provider, String provider_tx_id,
                                    double balance, Instant startTime){
            Map<String, Object> balanceInfo2 = WalletUtils.getPlayerBalance(userId);
            String newBalance = balanceInfo2.get("balance").toString();

            Map<String, Object> data = new HashMap<>();
            data.put("operator_tx_id", operator_tx_id);
            data.put("new_balance", newBalance);
            data.put("old_balance", balance);
            data.put("user_id", userId);
            data.put("currency", props.softCurrency());
            data.put("provider", provider);
            data.put("provider_tx_id", provider_tx_id);

            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", "OK");
            response.put("data", data);
            return Response.ok(response.toString()).type(MediaType.APPLICATION_JSON).build();
        }
}
