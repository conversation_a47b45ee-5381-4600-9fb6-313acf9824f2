package com.mossbets.integrations.entities.transactions;

import io.quarkus.hibernate.orm.panache.PanacheEntity;
import io.quarkus.hibernate.orm.PersistenceUnit;
import jakarta.persistence.*;
import java.time.LocalDate;
import java.math.BigDecimal;
import java.time.LocalDateTime;


@Entity
@Table(name = "transaction_leaderboard_stats", schema = "mossbets_transactions")
public class LeaderboardStats {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    public Long id;

    @Column(name = "promo_id", nullable = false)
    public Integer promoId;

    @Column(name = "profile_id", nullable = false)
    public String profileId;

    @Column(name = "bet_count", nullable = false)
    public Long betCount;

    @Column(name = "stake")
    public BigDecimal stake;

    @Column(name = "odds", nullable = false)
    public BigDecimal odds;

    @Column(name = "status", nullable = false)
    public Short status;

    @Column(name = "promo_date")
    public LocalDate promoDate;

    @Column(name = "created_at", nullable = false)
    public LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    public LocalDateTime updatedAt;

    // Getters and setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getPromoId() {
        return promoId;
    }

    public void setPromoId(Integer promoId) {
        this.promoId = promoId;
    }

    public String getProfileId() {
        return profileId;
    }

    public void setProfileId(String profileId) {
        this.profileId = profileId;
    }

    public Long getBetCount() {
        return betCount;
    }

    public void setBetCount(Long betCount) {
        this.betCount = betCount;
    }

    public BigDecimal getStake() {
        return stake;
    }

    public void setStake(BigDecimal stake) {
        this.stake = stake;
    }

    public BigDecimal getOdds() {
        return odds;
    }

    public void setOdds(BigDecimal odds) {
        this.odds = odds;
    }

    public Short getStatus() {
        return status;
    }

    public void setStatus(Short status) {
        this.status = status;
    }

    public LocalDate getPromoDate() {
        return promoDate;
    }

    public void setPromoDate(LocalDate promoDate) {
        this.promoDate = promoDate;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
}