package com.mossbets.integrations.entities.bonus;

import io.quarkus.hibernate.orm.panache.PanacheEntity;
import jakarta.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "promotion", schema = "mossbets_bonus")
public class Promotion extends PanacheEntity {

    @Column(name = "promo_type_id", insertable=false, updatable=false)
    public Integer promoTypeId;
    
    @Column(name = "promo_name")
    public String promoName;
    
    @Column(name = "promo_url")
    public String promoUrl;
    
    @Column(name = "promo_details")
    public String promoDetails;
    
    @Column(name = "promo_images")
    public String promoImages;
    
    public Integer status;
    
    @Column(name = "starting_date")
    public LocalDateTime startingDate;
    
    @Column(name = "ending_date")
    public LocalDateTime endingDate;
    
    @Column(name = "created_at")
    public LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    public LocalDateTime updatedAt;

    @ManyToOne
    @JoinColumn(name = "promo_type_id")
    public PromotionType promotionType;
}