package com.mossbets.integrations.entities.bonus;

import io.quarkus.hibernate.orm.panache.PanacheEntity;
import jakarta.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "promotion_type", schema = "mossbets_bonus")
public class PromotionType extends PanacheEntity {

    @Column(name = "id")
    public Long id;
    @Column(name = "component")
    public String component;
    public String name;
    public String description;
    public Integer status;
    
    @Column(name = "created_at")
    public LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    public LocalDateTime updatedAt;
}