package com.mossbets.integrations.entities.bonus;

import io.quarkus.hibernate.orm.panache.PanacheEntity;
import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "promotion_settings", schema = "mossbets_bonus")
public class PromotionSettings extends PanacheEntity {
    @Column(name = "promo_id", insertable = false, updatable = false)
    public Integer promoId;
    
    @Column(name = "min_stake")
    public BigDecimal minStake;
    
    @Column(name = "min_odds")
    public BigDecimal minOdds;
    
    public Integer status;
    
    @Column(name = "created_at")
    public LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    public LocalDateTime updatedAt;

    @ManyToOne
    @JoinColumn(name = "promo_id")
    public Promotion promotion;
}