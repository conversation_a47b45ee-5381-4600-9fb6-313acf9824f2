package com.mossbets.integrations.entities.bets;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "bet_limits", schema = "mossbets_bets")
public class BetLimit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "client_id", nullable = false)
    private Integer clientId;

    @Column(name = "bet_name", nullable = false, length = 45)
    private String betName;

    @Column(name = "currency", length = 4)
    private String currency;

    @Column(name = "max_stake", nullable = false)
    private BigDecimal maxStake;

    @Column(name = "min_stake", nullable = false)
    private BigDecimal minStake;

    @Column(name = "max_win", nullable = false)
    private BigDecimal maxWin;

    @Column(name = "risk_approval_amount", nullable = false)
    private BigDecimal riskApprovalAmount;

    @Column(name = "description", length = 200)
    private String description;

    @Column(name = "extra_settings", columnDefinition = "TEXT")
    private String extraSettings;

    @Column(name = "status", nullable = false)
    private Integer status;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // Getters and setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getClientId() {
        return clientId;
    }

    public void setClientId(Integer clientId) {
        this.clientId = clientId;
    }

    public String getBetName() {
        return betName;
    }

    public void setBetName(String betName) {
        this.betName = betName;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public BigDecimal getMaxStake() {
        return maxStake;
    }

    public void setMaxStake(BigDecimal maxStake) {
        this.maxStake = maxStake;
    }

    public BigDecimal getMinStake() {
        return minStake;
    }

    public void setMinStake(BigDecimal minStake) {
        this.minStake = minStake;
    }

    public BigDecimal getMaxWin() {
        return maxWin;
    }

    public void setMaxWin(BigDecimal maxWin) {
        this.maxWin = maxWin;
    }

    public BigDecimal getRiskApprovalAmount() {
        return riskApprovalAmount;
    }

    public void setRiskApprovalAmount(BigDecimal riskApprovalAmount) {
        this.riskApprovalAmount = riskApprovalAmount;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getExtraSettings() {
        return extraSettings;
    }

    public void setExtraSettings(String extraSettings) {
        this.extraSettings = extraSettings;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
}