package com.mossbets.integrations.entities.bets;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "spribe_bets", schema = "mossbets_bets", indexes = {
        @Index(name = "idx_client_id", columnList = "client_id"),
        @Index(name = "idx_profile_id", columnList = "profile_id"),
        @Index(name = "idx_bet_amount", columnList = "bet_amount"),
        @Index(name = "idx_bet_reference", columnList = "bet_reference"),
        @Index(name = "idx_bet_transaction_id", columnList = "bet_transaction_id"),
        @Index(name = "idx_bet_type", columnList = "bet_type"),
        @Index(name = "idx_possible_win", columnList = "possible_win"),
        @Index(name = "idx_created_by", columnList = "created_by"),
        @Index(name = "idx_created_at", columnList = "created_at")
})
public class SpribeBet {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "bet_id", nullable = false)
    private Long betId;

    @Column(name = "client_id", nullable = false)
    private Integer clientId = 1;

    @Column(name = "profile_id", nullable = false, length = 255)
    private String profileId;

    @Column(name = "bet_currency", nullable = false, length = 3)
    private String betCurrency;

    @Column(name = "bet_amount", nullable = false, precision = 38, scale = 2)
    private BigDecimal betAmount;

    @Column(name = "bet_reference", nullable = false, length = 255)
    private String betReference;

    @Column(name = "bet_transaction_id", nullable = false, length = 255)
    private String betTransactionId;

    @Column(name = "bet_credit_transaction_id", length = 255)
    private String betCreditTransactionId;

    @Column(name = "bet_type", nullable = false)
    private Short betType;

    @Column(name = "provider_name", nullable = false, length = 80)
    private String providerName;

    @Column(name = "action_id", length = 255)
    private String actionId;

    @Column(name = "internal_game_id", length = 255)
    private String internalGameId;

    @Column(name = "credit_provider_id", length = 255)
    private String creditProviderId;

    @Column(name = "total_games", nullable = false)
    private Integer totalGames = 1;

    @Column(name = "total_odd", precision = 38, scale = 2)
    private BigDecimal totalOdd;

    @Column(name = "possible_win", nullable = false, precision = 38, scale = 2)
    private BigDecimal possibleWin;

    @Column(name = "witholding_tax", nullable = false, precision = 38, scale = 2)
    private BigDecimal witholdingTax;

    @Column(name = "excise_tax", nullable = false, precision = 38, scale = 2)
    private BigDecimal exciseTax;

    @Column(name = "created_by", nullable = false, length = 70)
    private String createdBy;

    @Column(name = "status")
    private Integer status = 0;

    @Column(name = "kra_report", nullable = false)
    private Short kraReport = 0;

    @Column(name = "extra_data", columnDefinition = "TEXT")
    private String extraData;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Column(name = "balance", columnDefinition = "TEXT")
    private String balance;

    // Getters and Setters
    public Long getBetId() {
        return betId;
    }

    public void setBetId(Long betId) {
        this.betId = betId;
    }

    public Integer getClientId() {
        return clientId;
    }

    public void setClientId(Integer clientId) {
        this.clientId = clientId;
    }

    public String getProfileId() {
        return profileId;
    }

    public void setProfileId(String profileId) {
        this.profileId = profileId;
    }

    public String getBetCurrency() {
        return betCurrency;
    }

    public void setBetCurrency(String betCurrency) {
        this.betCurrency = betCurrency;
    }

    public BigDecimal getBetAmount() {
        return betAmount;
    }

    public void setBetAmount(BigDecimal betAmount) {
        this.betAmount = betAmount;
    }

    public String getBetReference() {
        return betReference;
    }

    public void setBetReference(String betReference) {
        this.betReference = betReference;
    }

    public String getBetTransactionId() {
        return betTransactionId;
    }

    public void setBetTransactionId(String betTransactionId) {
        this.betTransactionId = betTransactionId;
    }

    public String getBetCreditTransactionId() {
        return betCreditTransactionId;
    }

    public void setBetCreditTransactionId(String betCreditTransactionId) {
        this.betCreditTransactionId = betCreditTransactionId;
    }

    public Short getBetType() {
        return betType;
    }

    public void setBetType(Short betType) {
        this.betType = betType;
    }

    public String getProviderName() {
        return providerName;
    }

    public void setProviderName(String providerName) {
        this.providerName = providerName;
    }

    public String getActionId() {
        return actionId;
    }

    public void setActionId(String actionId) {
        this.actionId = actionId;
    }

    public String getInternalGameId() {
        return internalGameId;
    }

    public void setInternalGameId(String internalGameId) {
        this.internalGameId = internalGameId;
    }

    public String getCreditProviderId() {
        return creditProviderId;
    }

    public void setCreditProviderId(String creditProviderId) {
        this.creditProviderId = creditProviderId;
    }

    public Integer getTotalGames() {
        return totalGames;
    }

    public void setTotalGames(Integer totalGames) {
        this.totalGames = totalGames;
    }

    public BigDecimal getTotalOdd() {
        return totalOdd;
    }

    public void setTotalOdd(BigDecimal totalOdd) {
        this.totalOdd = totalOdd;
    }

    public BigDecimal getPossibleWin() {
        return possibleWin;
    }

    public void setPossibleWin(BigDecimal possibleWin) {
        this.possibleWin = possibleWin;
    }

    public BigDecimal getWitholdingTax() {
        return witholdingTax;
    }

    public void setWitholdingTax(BigDecimal witholdingTax) {
        this.witholdingTax = witholdingTax;
    }

    public BigDecimal getExciseTax() {
        return exciseTax;
    }

    public void setExciseTax(BigDecimal exciseTax) {
        this.exciseTax = exciseTax;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Short getKraReport() {
        return kraReport;
    }

    public void setKraReport(Short kraReport) {
        this.kraReport = kraReport;
    }

    public String getExtraData() {
        return extraData;
    }

    public void setExtraData(String extraData) {
        this.extraData = extraData;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getBalance() {
        return balance;
    }

    public void setBalance(String balance) {
        this.balance = balance;
    }
}