package com.mossbets.integrations.entities.profile;

import io.quarkus.hibernate.orm.panache.PanacheEntity;
import io.quarkus.hibernate.orm.PersistenceUnit;
import jakarta.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "profile_login", schema = "mossbets_profile")
public class ProfileLogin {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "profile_id", nullable = false, unique = true)
    private String profileId;

    @Column(name = "verification_code", length = 100)
    private String verificationCode;

    @Column(name = "password", nullable = false, length = 250)
    private String password;

    @Column(name = "access_token", nullable = false, unique = true, length = 700)
    private String accessToken;

    @Column(name = "success_attempts", nullable = false)
    private Integer successAttempts;

    @Column(name = "failed_attempts", nullable = false)
    private Integer failedAttempts;

    @Column(name = "successful_resets", nullable = false)
    private Integer successfulResets;

    @Column(name = "reset_attempts", nullable = false)
    private Integer resetAttempts;

    @Column(name = "failed_verify_attempts", nullable = false)
    private Integer failedVerifyAttempts;

    @Column(name = "status", nullable = false)
    private Integer status;

    @Column(name = "frequency_of_use")
    private Integer frequencyOfUse;

    @Column(name = "token_expiry_date")
    private LocalDateTime tokenExpiryDate;

    @Column(name = "verify_blocked_timeline")
    private LocalDateTime verifyBlockedTimeline;

    @Column(name = "last_login_date")
    private LocalDateTime lastLoginDate;

    @Column(name = "last_reset_date")
    private LocalDateTime lastResetDate;

    @Column(name = "last_failed_reset_date")
    private LocalDateTime lastFailedResetDate;

    @Column(name = "last_verified_date")
    private LocalDateTime lastVerifiedDate;

    @Column(name = "blocked_timeline")
    private LocalDateTime blockedTimeline;

    @Column(name = "last_use_date")
    private LocalDateTime lastUseDate;

    @Column(name = "exclusion_period_date")
    private LocalDateTime exclusionPeriodDate;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // Getters and setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getProfileId() {
        return profileId;
    }

    public void setProfileId(String profileId) {
        this.profileId = profileId;
    }

    public String getVerificationCode() {
        return verificationCode;
    }

    public void setVerificationCode(String verificationCode) {
        this.verificationCode = verificationCode;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public Integer getSuccessAttempts() {
        return successAttempts;
    }

    public void setSuccessAttempts(Integer successAttempts) {
        this.successAttempts = successAttempts;
    }

    public Integer getFailedAttempts() {
        return failedAttempts;
    }

    public void setFailedAttempts(Integer failedAttempts) {
        this.failedAttempts = failedAttempts;
    }

    public Integer getSuccessfulResets() {
        return successfulResets;
    }

    public void setSuccessfulResets(Integer successfulResets) {
        this.successfulResets = successfulResets;
    }

    public Integer getResetAttempts() {
        return resetAttempts;
    }

    public void setResetAttempts(Integer resetAttempts) {
        this.resetAttempts = resetAttempts;
    }

    public Integer getFailedVerifyAttempts() {
        return failedVerifyAttempts;
    }

    public void setFailedVerifyAttempts(Integer failedVerifyAttempts) {
        this.failedVerifyAttempts = failedVerifyAttempts;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getFrequencyOfUse() {
        return frequencyOfUse;
    }

    public void setFrequencyOfUse(Integer frequencyOfUse) {
        this.frequencyOfUse = frequencyOfUse;
    }

    public LocalDateTime getTokenExpiryDate() {
        return tokenExpiryDate;
    }

    public void setTokenExpiryDate(LocalDateTime tokenExpiryDate) {
        this.tokenExpiryDate = tokenExpiryDate;
    }

    public LocalDateTime getVerifyBlockedTimeline() {
        return verifyBlockedTimeline;
    }

    public void setVerifyBlockedTimeline(LocalDateTime verifyBlockedTimeline) {
        this.verifyBlockedTimeline = verifyBlockedTimeline;
    }

    public LocalDateTime getLastLoginDate() {
        return lastLoginDate;
    }

    public void setLastLoginDate(LocalDateTime lastLoginDate) {
        this.lastLoginDate = lastLoginDate;
    }

    public LocalDateTime getLastResetDate() {
        return lastResetDate;
    }

    public void setLastResetDate(LocalDateTime lastResetDate) {
        this.lastResetDate = lastResetDate;
    }

    public LocalDateTime getLastFailedResetDate() {
        return lastFailedResetDate;
    }

    public void setLastFailedResetDate(LocalDateTime lastFailedResetDate) {
        this.lastFailedResetDate = lastFailedResetDate;
    }

    public LocalDateTime getLastVerifiedDate() {
        return lastVerifiedDate;
    }

    public void setLastVerifiedDate(LocalDateTime lastVerifiedDate) {
        this.lastVerifiedDate = lastVerifiedDate;
    }

    public LocalDateTime getBlockedTimeline() {
        return blockedTimeline;
    }

    public void setBlockedTimeline(LocalDateTime blockedTimeline) {
        this.blockedTimeline = blockedTimeline;
    }

    public LocalDateTime getLastUseDate() {
        return lastUseDate;
    }

    public void setLastUseDate(LocalDateTime lastUseDate) {
        this.lastUseDate = lastUseDate;
    }

    public LocalDateTime getExclusionPeriodDate() {
        return exclusionPeriodDate;
    }

    public void setExclusionPeriodDate(LocalDateTime exclusionPeriodDate) {
        this.exclusionPeriodDate = exclusionPeriodDate;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
}