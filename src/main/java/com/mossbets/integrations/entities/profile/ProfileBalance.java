package com.mossbets.integrations.entities.profile;

import io.quarkus.hibernate.orm.panache.PanacheEntity;
import jakarta.persistence.*;
import io.quarkus.hibernate.orm.PersistenceUnit;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "profile_balance", schema = "mossbets_profile")
public class ProfileBalance {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    public Long id;

    @Column(name = "profile_id", nullable = false, unique = true)
    public String profileId;

    @Column(name = "currency", nullable = false, length = 10)
    public String currency;

    @Column(name = "balance", nullable = false)
    public BigDecimal balance;

    @Column(name = "bonus", nullable = false)
    public BigDecimal bonus;

    @Column(name = "status", nullable = false)
    public Integer status;

    @Column(name = "created_at", nullable = false)
    public LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    public LocalDateTime updatedAt;

    // Getters and setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getProfileId() {
        return profileId;
    }

    public void setProfileId(String profileId) {
        this.profileId = profileId;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public BigDecimal getBalance() {
        return balance;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }

    public BigDecimal getBonus() {
        return bonus;
    }

    public void setBonus(BigDecimal bonus) {
        this.bonus = bonus;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
}