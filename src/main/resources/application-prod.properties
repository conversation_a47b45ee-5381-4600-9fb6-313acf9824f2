#HTTP Apache
quarkus.http.port=8080

#Datasource MASTER
#
#Datasource profile
quarkus.datasource."profile".db-kind=mysql
quarkus.datasource."profile".username=${DB_USERNAME}
quarkus.datasource."profile".password=${DB_PASSWORD}
quarkus.datasource."profile".jdbc.url=jdbc:mysql://${DB_WRITE}:3306/mossbets_profile?useSSL=false&autoreconnect=true&useUnicode=true&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&relaxAutoCommit=true&useServerPrepStmts=false&rewriteBatchedStatements=true&character_set_server=utf8mb4&allowPublicKeyRetrieval=true
quarkus.hibernate-orm.profile.packages=com.mossbets.integrations.entities.profile
quarkus.hibernate-orm.profile.database.generation=none
quarkus.hibernate-orm.profile.datasource=profile

# Datasource for bets
quarkus.datasource."bets".db-kind=mysql
quarkus.datasource."bets".username=${DB_USERNAME}
quarkus.datasource."bets".password=${DB_PASSWORD}
quarkus.datasource."bets".jdbc.url=jdbc:mysql://${DB_WRITE}:3306/mossbets_bets?useSSL=false&autoreconnect=true&useUnicode=true&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&relaxAutoCommit=true&useServerPrepStmts=false&rewriteBatchedStatements=true&character_set_server=utf8mb4&allowPublicKeyRetrieval=true
quarkus.hibernate-orm.bets.packages=com.mossbets.integrations.entities.bets
quarkus.hibernate-orm.bets.database.generation=none
quarkus.hibernate-orm.bets.datasource=bets

# Datasource for transactions
quarkus.datasource."transactions".db-kind=mysql
quarkus.datasource."transactions".username=${DB_USERNAME}
quarkus.datasource."transactions".password=${DB_PASSWORD}
quarkus.datasource."transactions".jdbc.url=jdbc:mysql://${DB_WRITE}:3306/mossbets_transactions?useSSL=false&autoreconnect=true&useUnicode=true&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&relaxAutoCommit=true&useServerPrepStmts=false&rewriteBatchedStatements=true&character_set_server=utf8mb4&allowPublicKeyRetrieval=true
quarkus.hibernate-orm.transactions.packages=com.mossbets.integrations.entities.transactions
quarkus.hibernate-orm.transactions.database.generation=none
quarkus.hibernate-orm.transactions.datasource=transactions

# Datasource for bonus
quarkus.datasource."bonus".db-kind=mysql
quarkus.datasource."bonus".username=${DB_USERNAME}
quarkus.datasource."bonus".password=${DB_PASSWORD}
quarkus.datasource."bonus".jdbc.url=jdbc:mysql://${DB_WRITE}:3306/mossbets_bonus?useSSL=false&autoreconnect=true&useUnicode=true&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&relaxAutoCommit=true&useServerPrepStmts=false&rewriteBatchedStatements=true&character_set_server=utf8mb4&allowPublicKeyRetrieval=true
quarkus.hibernate-orm.bonus.packages=com.mossbets.integrations.entities.bonus
quarkus.hibernate-orm.bonus.database.generation=none
quarkus.hibernate-orm.bonus.datasource=bonus

# Hibernate ORM configuration
#quarkus.hibernate-orm.profile.database.generation=none
quarkus.hibernate-orm.profile.log.sql=false
#quarkus.hibernate-orm.profile.datasource=profile

# Hibernate ORM configuration for persistence unit 'profile'
#quarkus.hibernate-orm.profile.packages=com.example.model
#quarkus.hibernate-orm.profile.database.generation=update

# Connection pooling (Agroal still used under the hood)
quarkus.datasource."profile".jdbc.initial-size=10
quarkus.datasource."profile".jdbc.max-size=50
quarkus.datasource."profile".jdbc.min-size=1

#Write Databases
%profile.quarkus.datasource."profile".active=true

#Read Databases
%profile-read.quarkus.datasource."profile-read".active=true

quarkus.datasource.devservices.enabled=false

#logging
quarkus.log.file.level=ALL
quarkus.log.file.enable=true
quarkus.log.console.enable=false
quarkus.log.file.path=/var/tmp/log/java/app.log
quarkus.log.file.format=%d{yyyy-MM-dd HH:mm:ss,SSS} : %-5p : %m%n
quarkus.log.file.rotation.max-file-size=5000M
quarkus.log.file.rotation.max-backup-index=10
quarkus.log.file.rotation.file-suffix=.yyyy-MM-dd
quarkus.log.file.rotation.rotate-on-boot=true

#Redis
mbs.redis-port=6379
mbs.redis-user=${REDIS_USER}
mbs.redis-auth=${REDIS_AUTH}
mbs.redis-host=${REDIS_HOST:ke-pr-redis-ha-1-node-0}

mbs.number-of-threads=200

#Rabbit
mbs.rabbit-mq-port=5672
mbs.rabbit-mq-password=${RABBIT_PASSWORD}
mbs.rabbit-mq-username=${RABBIT_USERNAME}
mbs.rabbit-mq-host=${RABBIT_HOST:rabbitmq-cluster-1-node-0}

#mail
mbs.server-name=${SERVERNAME}
mbs.launch-url=${LAUNCHURL}
mbs.cipher-url=${CIPHERURL}
mbs.encryption-key=${ENVRYPTIONKEY}

# OpenTelemetry Tracing
quarkus.application.name=mbs-quarkus-app
quarkus.otel.exporter.otlp.endpoint=${OTEL-COLLECTOR-URL}
quarkus.otel.traces.exporter=otlp
quarkus.otel.metrics.enabled=true
quarkus.micrometer.export.prometheus.enabled=true
quarkus.micrometer.export.prometheus.path=/q/metrics
quarkus.micrometer.export.prometheus.include-trace-exemplars=true
# Logging with Trace Context
quarkus.log.console.format=%d{HH:mm:ss} %-5p traceId=%X{traceId}, parentId=%X{parentId}, spanId=%X{spanId}, sampled=%X{sampled} [%c{2.}] (%t) %s%e%n
quarkus.micrometer.binder.http-server.enabled=true
quarkus.datasource.jdbc.telemetry=true