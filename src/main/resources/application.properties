#App
quarkus.application.version=1.0.1

#HTTP Apache
quarkus.http.port=8094
quarkus.http.host=0.0.0.0
quarkus.http.http2=true
quarkus.http.enable-compression=true
quarkus.http.limits.max-connections=1000
quarkus.http.limits.max-body-size = 2M

# # #CORS(Do not alter any parameters here unless instructed to)
# quarkus.http.cors=true
# quarkus.http.cors.origins=/.*/
# quarkus.http.cors.methods=GET,POST
# quarkus.http.cors.access-control-max-age=24H
# quarkus.http.cors.access-control-allow-credentials=true
# quarkus.http.cors.headers=accept,authorization,content-type,x-hash-key,x-requested-with,x-signature,x-api-key,x-access,x-app-key,x-service-key,x-timezone,x-timestamp,xmlhttprequest,access-control-allow-origin

# #Headers
# quarkus.http.header."Connection".value=keep-alive,Keep-Alive
# quarkus.http.header."Keep-Alive".value=timeout=5,max=500
# #quarkus.http.header."Transfer-Encoding".value=chunked
# quarkus.http.header."X-Content-Type-Options".value=nosniff
# quarkus.http.header."Access-Control-Allow-Methods".value=GET,POST,OPTIONS
# quarkus.http.header."Access-Control-Request-Headers".value=Authorization,Origin,Content-Type,Accept,Content-Disposition

#CORS - Allow all requests
quarkus.http.cors=true
quarkus.http.cors.origins=*
quarkus.http.cors.methods=*
quarkus.http.cors.headers=*
quarkus.http.cors.access-control-allow-credentials=false

# Headers
quarkus.http.header."Connection".value=keep-alive,Keep-Alive
quarkus.http.header."Keep-Alive".value=timeout=5,max=500
quarkus.http.header."X-Content-Type-Options".value=nosniff



# Other Performance Optimizations
quarkus.thread-pool.core-threads=100 
quarkus.thread-pool.max-threads=200
quarkus.thread-pool.queue-size=1024

# Metrics and Health Checks
quarkus.datasource.metrics.enabled=false

#
#Datasource profile
quarkus.datasource."profile".db-kind=mysql
quarkus.datasource."profile".username=apps_user
quarkus.datasource."profile".password=Tb#<M#BnvBc%ur5q
quarkus.datasource."profile".jdbc.url=***********************************************************************************************************************************************************************************************************************************************************************************************
quarkus.hibernate-orm.profile.packages=com.mossbets.integrations.entities.profile
quarkus.hibernate-orm.profile.database.generation=none 
quarkus.hibernate-orm.profile.datasource=profile

# Datasource for bets
quarkus.datasource."bets".db-kind=mysql
quarkus.datasource."bets".username=apps_user
quarkus.datasource."bets".password=Tb#<M#BnvBc%ur5q
quarkus.datasource."bets".jdbc.url=********************************************************************************************************************************************************************************************************************************************************************************************
quarkus.hibernate-orm.bets.packages=com.mossbets.integrations.entities.bets
quarkus.hibernate-orm.bets.database.generation=none
quarkus.hibernate-orm.bets.datasource=bets

# Datasource for transactions
quarkus.datasource."transactions".db-kind=mysql
quarkus.datasource."transactions".username=apps_user
quarkus.datasource."transactions".password=Tb#<M#BnvBc%ur5q
quarkus.datasource."transactions".jdbc.url=****************************************************************************************************************************************************************************************************************************************************************************************************
quarkus.hibernate-orm.transactions.packages=com.mossbets.integrations.entities.transactions
quarkus.hibernate-orm.transactions.database.generation=none
quarkus.hibernate-orm.transactions.datasource=transactions

# Datasource for bonus
quarkus.datasource."bonus".db-kind=mysql
quarkus.datasource."bonus".username=apps_user
quarkus.datasource."bonus".password=Tb#<M#BnvBc%ur5q
quarkus.datasource."bonus".jdbc.url=*********************************************************************************************************************************************************************************************************************************************************************************************
quarkus.hibernate-orm.bonus.packages=com.mossbets.integrations.entities.bonus
quarkus.hibernate-orm.bonus.database.generation=none
quarkus.hibernate-orm.bonus.datasource=bonus

# Hibernate ORM configuration
#quarkus.hibernate-orm.profile.database.generation=none
quarkus.hibernate-orm.profile.log.sql=false
#quarkus.hibernate-orm.profile.datasource=profile

# Hibernate ORM configuration for persistence unit 'profile'
#quarkus.hibernate-orm.profile.packages=com.example.model
#quarkus.hibernate-orm.profile.database.generation=update

# Connection pooling (Agroal still used under the hood)
quarkus.datasource."profile".jdbc.initial-size=10
quarkus.datasource."profile".jdbc.max-size=50
quarkus.datasource."profile".jdbc.min-size=1

#Write Databases
%profile.quarkus.datasource."profile".active=true

#Read Databases  
%profile-read.quarkus.datasource."profile-read".active=true

quarkus.datasource.devservices.enabled=false

#logging
quarkus.log.file.level=INFO
quarkus.log.file.enable=true
quarkus.log.console.enable=false
quarkus.log.file.path=/var/log/java/spribe.log
quarkus.log.file.format=%d{yyyy-MM-dd HH:mm:ss,SSS} : %-5p : %c:%L : %m%n
quarkus.log.file.rotation.max-file-size=5000M
quarkus.log.file.rotation.max-backup-index=10
quarkus.log.file.rotation.file-suffix=.yyyy-MM-dd
quarkus.log.file.rotation.rotate-on-boot=true

#Enable HTTP access logs
quarkus.http.access-log.enabled=true
quarkus.http.access-log.pattern=%h %l %u %t "%r" %s %b "%{i,Referer}" "%{i,User-Agent}" "%{i,X-Organization-Id}"
quarkus.log.category."io.quarkus.http.access".level=INFO
quarkus.http.access-log.category=io.quarkus.http.access
quarkus.http.access-log.log-to-file=true
quarkus.http.access-log.base-file-name=mbs-access
quarkus.http.access-log.log-directory=/var/log/java/

# Native Executable (if applicable)
#quarkus.native.enabled=true

quarkus.container-image.registry=europe-west1-docker.pkg.dev
quarkus.container-image.group=broker-server-341612/images
quarkus.container-image.build=false
quarkus.container-image.name=mbs-spribe-app
quarkus.jib.base-jvm-image=registry.access.redhat.com/ubi8/openjdk-21-runtime:1.20
quarkus.container-image.push=false

# Other Optimizations
quarkus.arc.remove-unused-beans=true
quarkus.live-reload.enabled=false

#Redis
#mbs.redis-port=6379
mbs.redis-user=${REDIS_USER:liden}
mbs.redis-auth=${REDIS_AUTH:eGuT7yrbJZ8d}
#mbs.redis-host=${REDIS_HOST:ke-pr-redis-ha-1-node-0}
#mbs.number-of-threads=200
#mbs.redis-max-idle=500
#mbs.redis-min-idle=100
quarkus.redis.hosts=${REDIS_HOST:redis://ke-pr-redis-ha-1-node-0:6379}
quarkus.redis.devservices.enabled=false
quarkus.redis.password=${REDIS_AUTH:eGuT7yrbJZ8d}

#Rabbit
mbs.rabbit-mq-port=5672
mbs.rabbit-mq-password=${RABBIT_PASSWORD:lID3n}
mbs.rabbit-mq-username=${RABBIT_USERNAME:liden}
mbs.rabbit-mq-host=${RABBIT_HOST:rabbitmq-cluster-1-vm}

#mail
mbs.server-name=${SERVERNAME:https://mossplay.games}
mbs.launch-url=${LAUNCHURL:https://litestaging.playbetman.com"}
mbs.cipher-url=${CIPHERURL:http://ke-pr-web-1:80/mply_api/init/v1/cipher}
mbs.encryption-key=${ENVRYPTIONKEY:RJBwYSb5dFcyqUEHD300jY9nhxmi6Lnx}

#
mbs.soft-secret=${SECRET:7t6w8x1lo387f6x230fj5a1if8i13phegceuw4695aantkrl9lyxrycj10f7r3mi}
mbs.soft-currency=KES
mbs.soft-api=${API:https://apitest.fundist.org/}
mbs.soft-apikey=${APIKEY:b52341b13c3cebdb8a7f0a2317491a7a}
mbs.soft-apipass=${APIPASS:4740990224750052}
mbs.soft-stake=40
mbs.soft-void=42
mbs.soft-payout=41
mbs.soft-refunds=43
mbs.soft-client-id=${CLIENTID:1}

quarkus.otel.enabled=false
quarkus.otel.logs.exporter=none
quarkus.otel.traces.exporter=none
quarkus.otel.metrics.enabled=false
quarkus.otel.exporter.otlp.endpoint=false
quarkus.datasource.jdbc.telemetry=false # Disable JDBC telemetry if not needed

# Configure multiple persistence units for Panache
#quarkus.hibernate-orm."main".datasource=db1
#quarkus.hibernate-orm."main".packages=com.mossbets.integrations.entities
#quarkus.hibernate-orm."main".database.generation=none

#quarkus.hibernate-orm."transactions".datasource=db2
#quarkus.hibernate-orm."transactions".packages=com.mossbets.integrations.entities
#quarkus.hibernate-orm."transactions".database.generation=none
#
#quarkus.hibernate-orm."bets".datasource=db3
#quarkus.hibernate-orm."bets".packages=com.mossbets.integrations.entities
#quarkus.hibernate-orm."bets".database.generation=none

# Disable default datasource since we use named datasources
quarkus.datasource.active=false

# Agroal connection pool logging
quarkus.log.category."io.agroal".level=DEBUG

# Narayana JTA transaction logging
quarkus.log.category."com.arjuna".level=TRACE



mbs.spribe-game-launch=${LAUNCH_URL:https://launch.spribegaming.com/}
mbs.spribe-operator-key=${OPERATOR_KEY:9U5auo0nki6gUzuGPgyLzxvv9zgOxv1w}
mbs.spribe-demo=${DEMO_URL:https://demo.spribe.io/launch/}
mbs.return-url='https://mossbets.com/'
mbs.return-url-dev='https://dev.v1.mossbets.bet/'
mbs.operator =${OPERATOR:mulabetscom}