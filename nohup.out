Picked up J<PERSON><PERSON>_TOOL_OPTIONS: --enable-native-access=ALL-UNNAMED
Picked up JAVA_TOOL_OPTIONS: --enable-native-access=ALL-<PERSON><PERSON>MED
Picked up JAVA_TOOL_OPTIONS: --enable-native-access=ALL-UNNAMED
[INFO] Scanning for projects...
[INFO] 
[INFO] --------------------< com.mossplay.games:mply_app >---------------------
[INFO] Building mbsi_app 1.0.1
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- resources:3.3.1:resources (default-cli) @ mply_app ---
[INFO] Copying 2 resources from src/main/resources to target/classes
[INFO] ------------------------------------------------------------------------
[INFO] BUILD SUCCESS
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  3.118 s
[INFO] Finished at: 2025-05-28T15:35:16+03:00
[INFO] ------------------------------------------------------------------------
Picked up JAVA_TOOL_OPTIONS: --enable-native-access=ALL-UNNAMED
[INFO] Scanning for projects...
[INFO] 
[INFO] --------------------< com.mossplay.games:mply_app >---------------------
[INFO] Building mbsi_app 1.0.1
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- quarkus:3.16.2:run (default-cli) @ mply_app ---
Executing "/root/.sdkman/candidates/java/23.0.2-oracle/bin/java -Dquarkus.platform.version=3.16.2 -Dquarkus.application.version=1.0.1 -Dquarkus.platform.artifact-id=quarkus-bom -Dquarkus.platform.group-id=io.quarkus.platform -Dquarkus.application.name=mply_app -jar /apps/quarkus/quakers_mbs/target/quarkus-app/quarkus-run.jar"
Picked up JAVA_TOOL_OPTIONS: --enable-native-access=ALL-UNNAMED
Configuration validation failed:
	java.util.NoSuchElementException: SRCFG00011: Could not expand value SERVERNAME in property mbs.server-name
	java.util.NoSuchElementException: SRCFG00011: Could not expand value ENVRYPTIONKEY in property mbs.encryption-key
	java.util.NoSuchElementException: SRCFG00011: Could not expand value RABBIT_PASSWORD in property mbs.rabbit-mq-password
	java.util.NoSuchElementException: SRCFG00011: Could not expand value REDIS_USER in property mbs.redis-user
	java.util.NoSuchElementException: SRCFG00011: Could not expand value RABBIT_USERNAME in property mbs.rabbit-mq-username
	java.util.NoSuchElementException: SRCFG00011: Could not expand value REDIS_AUTH in property mbs.redis-auth
	java.util.NoSuchElementException: SRCFG00011: Could not expand value CIPHERURL in property mbs.cipher-url
	java.util.NoSuchElementException: SRCFG00011: Could not expand value LAUNCHURL in property mbs.launch-url
[INFO] ------------------------------------------------------------------------
[INFO] BUILD SUCCESS
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  11.334 s
[INFO] Finished at: 2025-05-28T15:35:29+03:00
[INFO] ------------------------------------------------------------------------
