Apache Maven 3.9.9 (8e8579a9e76f7d015ee5ec7bfcdc97d260186937)
Maven home: /usr/local/Cellar/maven/3.9.9/libexec
Java version: 23.0.2, vendor: Homebrew, runtime: /usr/local/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home
Default locale: en_KE, platform encoding: UTF-8
OS name: "mac os x", version: "15.5", arch: "x86_64", family: "mac"
[DEBUG] Created new class realm maven.api
[DEBUG] Importing foreign packages into class realm maven.api
[DEBUG]   Imported: javax.annotation.* < plexus.core
[DEBUG]   Imported: javax.annotation.security.* < plexus.core
[DEBUG]   Imported: javax.inject.* < plexus.core
[DEBUG]   Imported: org.apache.maven.* < plexus.core
[DEBUG]   Imported: org.apache.maven.artifact < plexus.core
[DEBUG]   Imported: org.apache.maven.classrealm < plexus.core
[DEBUG]   Imported: org.apache.maven.cli < plexus.core
[DEBUG]   Imported: org.apache.maven.configuration < plexus.core
[DEBUG]   Imported: org.apache.maven.exception < plexus.core
[DEBUG]   Imported: org.apache.maven.execution < plexus.core
[DEBUG]   Imported: org.apache.maven.execution.scope < plexus.core
[DEBUG]   Imported: org.apache.maven.graph < plexus.core
[DEBUG]   Imported: org.apache.maven.lifecycle < plexus.core
[DEBUG]   Imported: org.apache.maven.model < plexus.core
[DEBUG]   Imported: org.apache.maven.monitor < plexus.core
[DEBUG]   Imported: org.apache.maven.plugin < plexus.core
[DEBUG]   Imported: org.apache.maven.profiles < plexus.core
[DEBUG]   Imported: org.apache.maven.project < plexus.core
[DEBUG]   Imported: org.apache.maven.reporting < plexus.core
[DEBUG]   Imported: org.apache.maven.repository < plexus.core
[DEBUG]   Imported: org.apache.maven.rtinfo < plexus.core
[DEBUG]   Imported: org.apache.maven.settings < plexus.core
[DEBUG]   Imported: org.apache.maven.toolchain < plexus.core
[DEBUG]   Imported: org.apache.maven.usability < plexus.core
[DEBUG]   Imported: org.apache.maven.wagon.* < plexus.core
[DEBUG]   Imported: org.apache.maven.wagon.authentication < plexus.core
[DEBUG]   Imported: org.apache.maven.wagon.authorization < plexus.core
[DEBUG]   Imported: org.apache.maven.wagon.events < plexus.core
[DEBUG]   Imported: org.apache.maven.wagon.observers < plexus.core
[DEBUG]   Imported: org.apache.maven.wagon.proxy < plexus.core
[DEBUG]   Imported: org.apache.maven.wagon.repository < plexus.core
[DEBUG]   Imported: org.apache.maven.wagon.resource < plexus.core
[DEBUG]   Imported: org.codehaus.classworlds < plexus.core
[DEBUG]   Imported: org.codehaus.plexus.* < plexus.core
[DEBUG]   Imported: org.codehaus.plexus.classworlds < plexus.core
[DEBUG]   Imported: org.codehaus.plexus.component < plexus.core
[DEBUG]   Imported: org.codehaus.plexus.configuration < plexus.core
[DEBUG]   Imported: org.codehaus.plexus.container < plexus.core
[DEBUG]   Imported: org.codehaus.plexus.context < plexus.core
[DEBUG]   Imported: org.codehaus.plexus.lifecycle < plexus.core
[DEBUG]   Imported: org.codehaus.plexus.logging < plexus.core
[DEBUG]   Imported: org.codehaus.plexus.personality < plexus.core
[DEBUG]   Imported: org.codehaus.plexus.util.xml.Xpp3Dom < plexus.core
[DEBUG]   Imported: org.codehaus.plexus.util.xml.pull.XmlPullParser < plexus.core
[DEBUG]   Imported: org.codehaus.plexus.util.xml.pull.XmlPullParserException < plexus.core
[DEBUG]   Imported: org.codehaus.plexus.util.xml.pull.XmlSerializer < plexus.core
[DEBUG]   Imported: org.eclipse.aether.* < plexus.core
[DEBUG]   Imported: org.eclipse.aether.artifact < plexus.core
[DEBUG]   Imported: org.eclipse.aether.collection < plexus.core
[DEBUG]   Imported: org.eclipse.aether.deployment < plexus.core
[DEBUG]   Imported: org.eclipse.aether.graph < plexus.core
[DEBUG]   Imported: org.eclipse.aether.impl < plexus.core
[DEBUG]   Imported: org.eclipse.aether.installation < plexus.core
[DEBUG]   Imported: org.eclipse.aether.internal.impl < plexus.core
[DEBUG]   Imported: org.eclipse.aether.metadata < plexus.core
[DEBUG]   Imported: org.eclipse.aether.repository < plexus.core
[DEBUG]   Imported: org.eclipse.aether.resolution < plexus.core
[DEBUG]   Imported: org.eclipse.aether.spi < plexus.core
[DEBUG]   Imported: org.eclipse.aether.transfer < plexus.core
[DEBUG]   Imported: org.eclipse.aether.util < plexus.core
[DEBUG]   Imported: org.eclipse.aether.version < plexus.core
[DEBUG]   Imported: org.fusesource.jansi.* < plexus.core
[DEBUG]   Imported: org.slf4j.* < plexus.core
[DEBUG]   Imported: org.slf4j.event.* < plexus.core
[DEBUG]   Imported: org.slf4j.helpers.* < plexus.core
[DEBUG]   Imported: org.slf4j.spi.* < plexus.core
[DEBUG] Populating class realm maven.api
[DEBUG] Created adapter factory; available factories [file-lock, rwlock-local, semaphore-local, noop]; available name mappers [discriminating, file-gav, file-hgav, file-static, gav, static]
[INFO] Error stacktraces are turned on.
[DEBUG] Message scheme: color
[DEBUG] Message styles: debug info warning error success failure strong mojo project
[DEBUG] Reading global settings from /usr/local/Cellar/maven/3.9.9/libexec/conf/settings.xml
[DEBUG] Reading user settings from /Users/<USER>/.m2/settings.xml
[DEBUG] Reading global toolchains from /usr/local/Cellar/maven/3.9.9/libexec/conf/toolchains.xml
[DEBUG] Reading user toolchains from /Users/<USER>/.m2/toolchains.xml
[DEBUG] Using local repository at /Users/<USER>/.m2/repository
[DEBUG] Using manager EnhancedLocalRepositoryManager with priority 10.0 for /Users/<USER>/.m2/repository
[INFO] Scanning for projects...
[DEBUG] Creating adapter using nameMapper 'gav' and factory 'rwlock-local'
[DEBUG] Using mirror maven-default-http-blocker (http://0.0.0.0/) for apache.snapshots (http://repository.apache.org/snapshots).
[DEBUG] Dependency collection stats {ConflictMarker.analyzeTime=11398250, ConflictMarker.markTime=2413333, ConflictMarker.nodeCount=1077, ConflictIdSorter.graphTime=3872542, ConflictIdSorter.topsortTime=452375, ConflictIdSorter.conflictIdCount=150, ConflictIdSorter.conflictIdCycleCount=0, ConflictResolver.totalTime=23133667, ConflictResolver.conflictItemCount=336, DfDependencyCollector.collectTime=1741890458, DfDependencyCollector.transformTime=45498333}
[DEBUG] io.quarkus.platform:quarkus-maven-plugin:jar:3.16.2
[DEBUG]    io.quarkus:quarkus-bootstrap-core:jar:3.16.2:compile
[DEBUG]       io.quarkus:quarkus-classloader-commons:jar:3.16.2:compile (version managed from default)
[DEBUG]       io.quarkus:quarkus-bootstrap-app-model:jar:3.16.2:compile (version managed from default)
[DEBUG]          org.jboss.logging:commons-logging-jboss-logging:jar:1.0.0.Final:runtime (version managed from default)
[DEBUG]       io.smallrye.common:smallrye-common-io:jar:2.7.0:compile (version managed from default)
[DEBUG]    io.quarkus:quarkus-bootstrap-maven-resolver:jar:3.16.2:compile
[DEBUG]       io.smallrye.beanbag:smallrye-beanbag-maven:jar:1.5.2:compile (version managed from default) (exclusions managed from default)
[DEBUG]          io.smallrye.beanbag:smallrye-beanbag-sisu:jar:1.5.2:compile
[DEBUG]             io.smallrye.beanbag:smallrye-beanbag:jar:1.5.2:compile
[DEBUG]          io.smallrye.common:smallrye-common-constraint:jar:2.7.0:compile (version managed from default)
[DEBUG]          commons-codec:commons-codec:jar:1.17.1:compile (version managed from default)
[DEBUG]          org.apache.httpcomponents:httpclient:jar:4.5.14:compile (version managed from default) (exclusions managed from default)
[DEBUG]          org.apache.httpcomponents:httpcore:jar:4.4.16:compile (version managed from default)
[DEBUG]          org.apache.maven:maven-artifact:jar:3.9.9:compile (version managed from default)
[DEBUG]          org.apache.maven:maven-builder-support:jar:3.9.9:compile (version managed from default)
[DEBUG]          org.apache.maven:maven-model:jar:3.9.9:compile (version managed from default)
[DEBUG]          org.apache.maven:maven-model-builder:jar:3.9.9:compile (version managed from default) (exclusions managed from default)
[DEBUG]          org.apache.maven:maven-repository-metadata:jar:3.9.9:compile (version managed from default)
[DEBUG]          org.apache.maven:maven-settings:jar:3.9.9:compile (version managed from default)
[DEBUG]          org.codehaus.plexus:plexus-interpolation:jar:1.26:compile (version managed from default)
[DEBUG]          org.codehaus.plexus:plexus-xml:jar:4.0.1:compile
[DEBUG]             org.apache.maven:maven-xml-impl:jar:4.0.0-alpha-5:compile
[DEBUG]                org.apache.maven:maven-api-xml:jar:4.0.0-alpha-5:compile
[DEBUG]                   org.apache.maven:maven-api-meta:jar:4.0.0-alpha-5:compile
[DEBUG]          org.codehaus.plexus:plexus-cipher:jar:2.0:compile (version managed from default)
[DEBUG]          org.codehaus.plexus:plexus-sec-dispatcher:jar:2.0:compile (version managed from default)
[DEBUG]       io.quarkus:quarkus-fs-util:jar:0.0.10:compile (version managed from default)
[DEBUG]       org.jboss.logmanager:jboss-logmanager:jar:3.0.6.Final:compile (version managed from default) (exclusions managed from default)
[DEBUG]          io.smallrye.common:smallrye-common-cpu:jar:2.7.0:compile (version managed from default)
[DEBUG]          io.smallrye.common:smallrye-common-expression:jar:2.7.0:compile (version managed from default)
[DEBUG]             io.smallrye.common:smallrye-common-function:jar:2.7.0:compile (version managed from default)
[DEBUG]          io.smallrye.common:smallrye-common-net:jar:2.7.0:compile (version managed from default)
[DEBUG]          io.smallrye.common:smallrye-common-ref:jar:2.7.0:compile (version managed from default)
[DEBUG]       org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile (version managed from default)
[DEBUG]       org.slf4j:slf4j-api:jar:2.0.6:compile (version managed from default)
[DEBUG]       org.apache.maven:maven-embedder:jar:3.9.9:compile (version managed from default) (exclusions managed from default)
[DEBUG]          org.apache.maven.shared:maven-shared-utils:jar:3.4.2:compile (version managed from default)
[DEBUG]          com.google.inject:guice:jar:5.1.0:compile
[DEBUG]             aopalliance:aopalliance:jar:1.0:compile
[DEBUG]          com.google.guava:guava:jar:33.3.1-jre:compile (version managed from default) (exclusions managed from default)
[DEBUG]          com.google.guava:failureaccess:jar:1.0.1:compile (version managed from default)
[DEBUG]          javax.annotation:javax.annotation-api:jar:1.3.2:compile (version managed from default)
[DEBUG]          org.codehaus.plexus:plexus-classworlds:jar:2.6.0:compile (version managed from default) (exclusions managed from default)
[DEBUG]          commons-cli:commons-cli:jar:1.8.0:compile
[DEBUG]       org.eclipse.sisu:org.eclipse.sisu.plexus:jar:0.9.0.M3:compile (version managed from default) (exclusions managed from default)
[DEBUG]       org.apache.maven:maven-settings-builder:jar:3.9.9:compile (version managed from default) (exclusions managed from default)
[DEBUG]       org.apache.maven:maven-resolver-provider:jar:3.9.9:compile (version managed from default) (exclusions managed from default)
[DEBUG]    io.quarkus:quarkus-core-deployment:jar:3.16.2:compile
[DEBUG]       org.aesh:readline:jar:2.6:compile (version managed from default)
[DEBUG]          org.fusesource.jansi:jansi:jar:2.4.0:compile (version managed from default)
[DEBUG]       org.aesh:aesh:jar:2.8.2:compile (version managed from default)
[DEBUG]       org.apache.commons:commons-lang3:jar:3.17.0:compile (version managed from default)
[DEBUG]       org.wildfly.common:wildfly-common:jar:1.7.0.Final:compile (version managed from default)
[DEBUG]       io.quarkus.gizmo:gizmo:jar:1.8.0:compile (version managed from default)
[DEBUG]          org.ow2.asm:asm-util:jar:9.7.1:compile (version managed from default)
[DEBUG]             org.ow2.asm:asm-analysis:jar:9.7.1:compile (version managed from default)
[DEBUG]       io.smallrye:jandex:jar:3.2.3:compile (version managed from default)
[DEBUG]       org.ow2.asm:asm:jar:9.7.1:compile (version managed from default)
[DEBUG]       org.ow2.asm:asm-commons:jar:9.7.1:compile (version managed from default)
[DEBUG]          org.ow2.asm:asm-tree:jar:9.7.1:compile (version managed from default)
[DEBUG]       io.quarkus:quarkus-development-mode-spi:jar:3.16.2:compile (version managed from default)
[DEBUG]       io.quarkus:quarkus-hibernate-validator-spi:jar:3.16.2:compile (version managed from default)
[DEBUG]       io.quarkus:quarkus-class-change-agent:jar:3.16.2:compile (version managed from default)
[DEBUG]       io.quarkus:quarkus-devtools-utilities:jar:3.16.2:compile (version managed from default)
[DEBUG]       org.eclipse.sisu:org.eclipse.sisu.inject:jar:0.9.0.M3:compile (version managed from default)
[DEBUG]       io.quarkus:quarkus-core:jar:3.16.2:compile (version managed from default)
[DEBUG]          jakarta.annotation:jakarta.annotation-api:jar:3.0.0:compile (version managed from default)
[DEBUG]          jakarta.inject:jakarta.inject-api:jar:2.0.1:compile (version managed from default)
[DEBUG]          io.smallrye.config:smallrye-config:jar:3.9.1:compile (version managed from default) (exclusions managed from default)
[DEBUG]             io.smallrye.config:smallrye-config-core:jar:3.9.1:compile (version managed from default)
[DEBUG]                org.eclipse.microprofile.config:microprofile-config-api:jar:3.1:compile (version managed from default) (exclusions managed from default)
[DEBUG]                io.smallrye.common:smallrye-common-classloader:jar:2.7.0:compile (version managed from default)
[DEBUG]                io.smallrye.config:smallrye-config-common:jar:3.9.1:compile (version managed from default)
[DEBUG]          org.jboss.logging:jboss-logging-annotations:jar:3.0.2.Final:compile (version managed from default)
[DEBUG]          org.jboss.threads:jboss-threads:jar:3.8.0.Final:compile (version managed from default)
[DEBUG]             io.smallrye.common:smallrye-common-annotation:jar:2.7.0:compile (version managed from default)
[DEBUG]          io.quarkus:quarkus-bootstrap-runner:jar:3.16.2:compile (version managed from default)
[DEBUG]             io.github.crac:org-crac:jar:0.1.3:compile (version managed from default)
[DEBUG]       io.quarkus:quarkus-builder:jar:3.16.2:compile (version managed from default)
[DEBUG]       org.graalvm.sdk:nativeimage:jar:23.1.2:compile (version managed from default)
[DEBUG]          org.graalvm.sdk:word:jar:23.1.2:compile
[DEBUG]       org.junit.platform:junit-platform-launcher:jar:1.10.5:compile (version managed from default)
[DEBUG]          org.junit.platform:junit-platform-engine:jar:1.10.5:compile (version managed from default)
[DEBUG]             org.opentest4j:opentest4j:jar:1.3.0:compile
[DEBUG]             org.junit.platform:junit-platform-commons:jar:1.10.5:compile (version managed from default)
[DEBUG]          org.apiguardian:apiguardian-api:jar:1.1.2:compile
[DEBUG]       org.junit.jupiter:junit-jupiter:jar:5.10.5:compile (version managed from default)
[DEBUG]          org.junit.jupiter:junit-jupiter-api:jar:5.10.5:compile (version managed from default)
[DEBUG]          org.junit.jupiter:junit-jupiter-params:jar:5.10.5:compile (version managed from default)
[DEBUG]          org.junit.jupiter:junit-jupiter-engine:jar:5.10.5:runtime (version managed from default)
[DEBUG]    io.quarkus:quarkus-project-core-extension-codestarts:jar:3.16.2:compile
[DEBUG]    io.quarkus:quarkus-devtools-common:jar:3.16.2:compile
[DEBUG]       io.quarkus:quarkus-devtools-registry-client:jar:3.16.2:compile (version managed from default)
[DEBUG]       io.quarkus:quarkus-devtools-base-codestarts:jar:3.16.2:compile
[DEBUG]       io.quarkus:quarkus-devtools-codestarts:jar:3.16.2:compile
[DEBUG]          io.quarkus.qute:qute-core:jar:3.16.2:compile (version managed from default)
[DEBUG]             io.smallrye.reactive:mutiny:jar:2.6.2:compile (version managed from default)
[DEBUG]                org.jctools:jctools-core:jar:4.0.5:compile (version managed from default)
[DEBUG]          commons-io:commons-io:jar:2.17.0:compile (version managed from default)
[DEBUG]       org.apache.commons:commons-compress:jar:1.27.1:compile (version managed from default)
[DEBUG]       io.smallrye.common:smallrye-common-version:jar:2.7.0:compile (version managed from default)
[DEBUG]       io.smallrye.common:smallrye-common-os:jar:2.7.0:compile (version managed from default)
[DEBUG]       io.fabric8:maven-model-helper:jar:37:compile
[DEBUG]          org.jdom:jdom2:jar:2.0.6.1:compile
[DEBUG]       com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.18.1:compile (version managed from default)
[DEBUG]          org.yaml:snakeyaml:jar:2.3:compile (version managed from default)
[DEBUG]       com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.18.1:compile (version managed from default)
[DEBUG]       org.apache.maven:maven-plugin-api:jar:3.9.9:compile (version managed from default)
[DEBUG]       org.apache.maven:maven-core:jar:3.9.9:compile (version managed from default) (exclusions managed from default)
[DEBUG]          org.apache.maven.resolver:maven-resolver-impl:jar:1.9.22:compile (version managed from default)
[DEBUG]             org.apache.maven.resolver:maven-resolver-named-locks:jar:1.9.22:compile
[DEBUG]          org.apache.maven.resolver:maven-resolver-api:jar:1.9.22:compile (version managed from default)
[DEBUG]          org.apache.maven.resolver:maven-resolver-spi:jar:1.9.22:compile (version managed from default)
[DEBUG]          org.apache.maven.resolver:maven-resolver-util:jar:1.9.22:compile (version managed from default)
[DEBUG]          org.codehaus.plexus:plexus-component-annotations:jar:2.1.0:compile (version managed from default)
[DEBUG]       jakarta.enterprise:jakarta.enterprise.cdi-api:jar:4.1.0:compile (version managed from default)
[DEBUG]          jakarta.enterprise:jakarta.enterprise.lang-model:jar:4.1.0:compile
[DEBUG]          jakarta.el:jakarta.el-api:jar:5.0.1:compile (version managed from default)
[DEBUG]          jakarta.interceptor:jakarta.interceptor-api:jar:2.2.0:compile (version managed from default) (exclusions managed from default)
[DEBUG]       org.codejive:java-properties:jar:0.0.7:compile
[DEBUG]    io.quarkus:quarkus-analytics-common:jar:3.16.2:compile
[DEBUG]       com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.18.1:compile (version managed from default)
[DEBUG]       io.quarkus:quarkus-devtools-message-writer:jar:3.16.2:compile
[DEBUG]    io.quarkus:quarkus-cyclonedx-generator:jar:3.16.2:compile
[DEBUG]       org.cyclonedx:cyclonedx-core-java:jar:9.0.5:compile (version managed from default)
[DEBUG]          org.apache.commons:commons-collections4:jar:4.4:compile
[DEBUG]          com.github.package-url:packageurl-java:jar:1.5.0:compile
[DEBUG]          com.fasterxml.jackson.dataformat:jackson-dataformat-xml:jar:2.18.1:compile (version managed from default)
[DEBUG]             org.codehaus.woodstox:stax2-api:jar:4.2.2:compile
[DEBUG]             com.fasterxml.woodstox:woodstox-core:jar:7.0.0:compile
[DEBUG]          com.networknt:json-schema-validator:jar:1.5.1:compile
[DEBUG]             com.ethlo.time:itu:jar:1.10.2:compile
[DEBUG]    javax.inject:javax.inject:jar:1:compile
[DEBUG]    org.freemarker:freemarker:jar:2.3.33:compile
[DEBUG]    org.eclipse.parsson:parsson:jar:1.1.7:compile
[DEBUG]       jakarta.json:jakarta.json-api:jar:2.1.3:compile (version managed from default)
[DEBUG]    com.fasterxml.jackson.core:jackson-databind:jar:2.18.1:compile
[DEBUG]       com.fasterxml.jackson.core:jackson-annotations:jar:2.18.1:compile (version managed from default)
[DEBUG]       com.fasterxml.jackson.core:jackson-core:jar:2.18.1:compile (version managed from default)
[DEBUG]    org.twdata.maven:mojo-executor:jar:2.4.0:compile
[DEBUG]       org.codehaus.plexus:plexus-utils:jar:3.5.1:compile (version managed from default)
[DEBUG]    org.jboss.slf4j:slf4j-jboss-logmanager:jar:2.0.0.Final:compile
[DEBUG] Created new class realm extension>io.quarkus.platform:quarkus-maven-plugin:3.16.2
[DEBUG] Importing foreign packages into class realm extension>io.quarkus.platform:quarkus-maven-plugin:3.16.2
[DEBUG]   Imported:  < maven.api
[DEBUG] Populating class realm extension>io.quarkus.platform:quarkus-maven-plugin:3.16.2
[DEBUG]   Included: io.quarkus.platform:quarkus-maven-plugin:jar:3.16.2
[DEBUG]   Included: io.quarkus:quarkus-bootstrap-core:jar:3.16.2
[DEBUG]   Included: io.quarkus:quarkus-classloader-commons:jar:3.16.2
[DEBUG]   Included: io.quarkus:quarkus-bootstrap-app-model:jar:3.16.2
[DEBUG]   Included: org.jboss.logging:commons-logging-jboss-logging:jar:1.0.0.Final
[DEBUG]   Included: io.smallrye.common:smallrye-common-io:jar:2.7.0
[DEBUG]   Included: io.quarkus:quarkus-bootstrap-maven-resolver:jar:3.16.2
[DEBUG]   Included: io.smallrye.beanbag:smallrye-beanbag-maven:jar:1.5.2
[DEBUG]   Included: io.smallrye.beanbag:smallrye-beanbag-sisu:jar:1.5.2
[DEBUG]   Included: io.smallrye.beanbag:smallrye-beanbag:jar:1.5.2
[DEBUG]   Included: io.smallrye.common:smallrye-common-constraint:jar:2.7.0
[DEBUG]   Included: commons-codec:commons-codec:jar:1.17.1
[DEBUG]   Included: org.apache.httpcomponents:httpclient:jar:4.5.14
[DEBUG]   Included: org.apache.httpcomponents:httpcore:jar:4.4.16
[DEBUG]   Included: org.apache.maven:maven-builder-support:jar:3.9.9
[DEBUG]   Included: org.codehaus.plexus:plexus-interpolation:jar:1.26
[DEBUG]   Included: org.codehaus.plexus:plexus-xml:jar:4.0.1
[DEBUG]   Included: org.apache.maven:maven-xml-impl:jar:4.0.0-alpha-5
[DEBUG]   Included: org.apache.maven:maven-api-xml:jar:4.0.0-alpha-5
[DEBUG]   Included: org.apache.maven:maven-api-meta:jar:4.0.0-alpha-5
[DEBUG]   Included: org.codehaus.plexus:plexus-cipher:jar:2.0
[DEBUG]   Included: org.codehaus.plexus:plexus-sec-dispatcher:jar:2.0
[DEBUG]   Included: io.quarkus:quarkus-fs-util:jar:0.0.10
[DEBUG]   Included: org.jboss.logmanager:jboss-logmanager:jar:3.0.6.Final
[DEBUG]   Included: io.smallrye.common:smallrye-common-cpu:jar:2.7.0
[DEBUG]   Included: io.smallrye.common:smallrye-common-expression:jar:2.7.0
[DEBUG]   Included: io.smallrye.common:smallrye-common-function:jar:2.7.0
[DEBUG]   Included: io.smallrye.common:smallrye-common-net:jar:2.7.0
[DEBUG]   Included: io.smallrye.common:smallrye-common-ref:jar:2.7.0
[DEBUG]   Included: org.jboss.logging:jboss-logging:jar:3.6.1.Final
[DEBUG]   Included: org.apache.maven:maven-embedder:jar:3.9.9
[DEBUG]   Included: org.apache.maven.shared:maven-shared-utils:jar:3.4.2
[DEBUG]   Included: com.google.inject:guice:jar:5.1.0
[DEBUG]   Included: aopalliance:aopalliance:jar:1.0
[DEBUG]   Included: com.google.guava:guava:jar:33.3.1-jre
[DEBUG]   Included: com.google.guava:failureaccess:jar:1.0.1
[DEBUG]   Included: commons-cli:commons-cli:jar:1.8.0
[DEBUG]   Included: io.quarkus:quarkus-core-deployment:jar:3.16.2
[DEBUG]   Included: org.aesh:readline:jar:2.6
[DEBUG]   Included: org.aesh:aesh:jar:2.8.2
[DEBUG]   Included: org.apache.commons:commons-lang3:jar:3.17.0
[DEBUG]   Included: org.wildfly.common:wildfly-common:jar:1.7.0.Final
[DEBUG]   Included: io.quarkus.gizmo:gizmo:jar:1.8.0
[DEBUG]   Included: org.ow2.asm:asm-util:jar:9.7.1
[DEBUG]   Included: org.ow2.asm:asm-analysis:jar:9.7.1
[DEBUG]   Included: io.smallrye:jandex:jar:3.2.3
[DEBUG]   Included: org.ow2.asm:asm:jar:9.7.1
[DEBUG]   Included: org.ow2.asm:asm-commons:jar:9.7.1
[DEBUG]   Included: org.ow2.asm:asm-tree:jar:9.7.1
[DEBUG]   Included: io.quarkus:quarkus-development-mode-spi:jar:3.16.2
[DEBUG]   Included: io.quarkus:quarkus-hibernate-validator-spi:jar:3.16.2
[DEBUG]   Included: io.quarkus:quarkus-class-change-agent:jar:3.16.2
[DEBUG]   Included: io.quarkus:quarkus-devtools-utilities:jar:3.16.2
[DEBUG]   Included: org.eclipse.sisu:org.eclipse.sisu.inject:jar:0.9.0.M3
[DEBUG]   Included: io.quarkus:quarkus-core:jar:3.16.2
[DEBUG]   Included: jakarta.annotation:jakarta.annotation-api:jar:3.0.0
[DEBUG]   Included: jakarta.inject:jakarta.inject-api:jar:2.0.1
[DEBUG]   Included: io.smallrye.config:smallrye-config:jar:3.9.1
[DEBUG]   Included: io.smallrye.config:smallrye-config-core:jar:3.9.1
[DEBUG]   Included: org.eclipse.microprofile.config:microprofile-config-api:jar:3.1
[DEBUG]   Included: io.smallrye.common:smallrye-common-classloader:jar:2.7.0
[DEBUG]   Included: io.smallrye.config:smallrye-config-common:jar:3.9.1
[DEBUG]   Included: org.jboss.logging:jboss-logging-annotations:jar:3.0.2.Final
[DEBUG]   Included: org.jboss.threads:jboss-threads:jar:3.8.0.Final
[DEBUG]   Included: io.smallrye.common:smallrye-common-annotation:jar:2.7.0
[DEBUG]   Included: io.quarkus:quarkus-bootstrap-runner:jar:3.16.2
[DEBUG]   Included: io.github.crac:org-crac:jar:0.1.3
[DEBUG]   Included: io.quarkus:quarkus-builder:jar:3.16.2
[DEBUG]   Included: org.graalvm.sdk:nativeimage:jar:23.1.2
[DEBUG]   Included: org.graalvm.sdk:word:jar:23.1.2
[DEBUG]   Included: org.junit.platform:junit-platform-launcher:jar:1.10.5
[DEBUG]   Included: org.junit.platform:junit-platform-engine:jar:1.10.5
[DEBUG]   Included: org.opentest4j:opentest4j:jar:1.3.0
[DEBUG]   Included: org.junit.platform:junit-platform-commons:jar:1.10.5
[DEBUG]   Included: org.apiguardian:apiguardian-api:jar:1.1.2
[DEBUG]   Included: org.junit.jupiter:junit-jupiter:jar:5.10.5
[DEBUG]   Included: org.junit.jupiter:junit-jupiter-api:jar:5.10.5
[DEBUG]   Included: org.junit.jupiter:junit-jupiter-params:jar:5.10.5
[DEBUG]   Included: org.junit.jupiter:junit-jupiter-engine:jar:5.10.5
[DEBUG]   Included: io.quarkus:quarkus-project-core-extension-codestarts:jar:3.16.2
[DEBUG]   Included: io.quarkus:quarkus-devtools-common:jar:3.16.2
[DEBUG]   Included: io.quarkus:quarkus-devtools-registry-client:jar:3.16.2
[DEBUG]   Included: io.quarkus:quarkus-devtools-base-codestarts:jar:3.16.2
[DEBUG]   Included: io.quarkus:quarkus-devtools-codestarts:jar:3.16.2
[DEBUG]   Included: io.quarkus.qute:qute-core:jar:3.16.2
[DEBUG]   Included: io.smallrye.reactive:mutiny:jar:2.6.2
[DEBUG]   Included: org.jctools:jctools-core:jar:4.0.5
[DEBUG]   Included: commons-io:commons-io:jar:2.17.0
[DEBUG]   Included: org.apache.commons:commons-compress:jar:1.27.1
[DEBUG]   Included: io.smallrye.common:smallrye-common-version:jar:2.7.0
[DEBUG]   Included: io.smallrye.common:smallrye-common-os:jar:2.7.0
[DEBUG]   Included: io.fabric8:maven-model-helper:jar:37
[DEBUG]   Included: org.jdom:jdom2:jar:2.0.6.1
[DEBUG]   Included: com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.18.1
[DEBUG]   Included: org.yaml:snakeyaml:jar:2.3
[DEBUG]   Included: com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.18.1
[DEBUG]   Included: org.apache.maven.resolver:maven-resolver-named-locks:jar:1.9.22
[DEBUG]   Included: org.codehaus.plexus:plexus-component-annotations:jar:2.1.0
[DEBUG]   Included: jakarta.enterprise:jakarta.enterprise.cdi-api:jar:4.1.0
[DEBUG]   Included: jakarta.enterprise:jakarta.enterprise.lang-model:jar:4.1.0
[DEBUG]   Included: jakarta.el:jakarta.el-api:jar:5.0.1
[DEBUG]   Included: jakarta.interceptor:jakarta.interceptor-api:jar:2.2.0
[DEBUG]   Included: org.codejive:java-properties:jar:0.0.7
[DEBUG]   Included: io.quarkus:quarkus-analytics-common:jar:3.16.2
[DEBUG]   Included: com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.18.1
[DEBUG]   Included: io.quarkus:quarkus-devtools-message-writer:jar:3.16.2
[DEBUG]   Included: io.quarkus:quarkus-cyclonedx-generator:jar:3.16.2
[DEBUG]   Included: org.cyclonedx:cyclonedx-core-java:jar:9.0.5
[DEBUG]   Included: org.apache.commons:commons-collections4:jar:4.4
[DEBUG]   Included: com.github.package-url:packageurl-java:jar:1.5.0
[DEBUG]   Included: com.fasterxml.jackson.dataformat:jackson-dataformat-xml:jar:2.18.1
[DEBUG]   Included: org.codehaus.woodstox:stax2-api:jar:4.2.2
[DEBUG]   Included: com.fasterxml.woodstox:woodstox-core:jar:7.0.0
[DEBUG]   Included: com.networknt:json-schema-validator:jar:1.5.1
[DEBUG]   Included: com.ethlo.time:itu:jar:1.10.2
[DEBUG]   Included: org.freemarker:freemarker:jar:2.3.33
[DEBUG]   Included: org.eclipse.parsson:parsson:jar:1.1.7
[DEBUG]   Included: jakarta.json:jakarta.json-api:jar:2.1.3
[DEBUG]   Included: com.fasterxml.jackson.core:jackson-databind:jar:2.18.1
[DEBUG]   Included: com.fasterxml.jackson.core:jackson-annotations:jar:2.18.1
[DEBUG]   Included: com.fasterxml.jackson.core:jackson-core:jar:2.18.1
[DEBUG]   Included: org.twdata.maven:mojo-executor:jar:2.4.0
[DEBUG]   Included: org.codehaus.plexus:plexus-utils:jar:3.5.1
[DEBUG]   Included: org.jboss.slf4j:slf4j-jboss-logmanager:jar:2.0.0.Final
[DEBUG] Extension realms for project com.mossplay.games:quakers_app:jar:1.0.1: [ClassRealm[extension>io.quarkus.platform:quarkus-maven-plugin:3.16.2, parent: jdk.internal.loader.ClassLoaders$AppClassLoader@18ff02e4]]
[DEBUG] Created new class realm project>com.mossplay.games:quakers_app:1.0.1
[DEBUG] Populating class realm project>com.mossplay.games:quakers_app:1.0.1
[DEBUG] Looking up lifecycle mappings for packaging jar from ClassRealm[project>com.mossplay.games:quakers_app:1.0.1, parent: ClassRealm[maven.api, parent: null]]
[DEBUG] Resolving plugin prefix quarkus from [org.apache.maven.plugins, org.codehaus.mojo]
[DEBUG] Resolved plugin prefix quarkus to io.quarkus.platform:quarkus-maven-plugin from POM com.mossplay.games:quakers_app:jar:1.0.1
[DEBUG] === REACTOR BUILD PLAN ================================================
[DEBUG] Project: com.mossplay.games:quakers_app:jar:1.0.1
[DEBUG] Tasks:   [quarkus:dev]
[DEBUG] Style:   Regular
[DEBUG] =======================================================================
[INFO] 
[INFO] -------------------< com.mossplay.games:quakers_app >-------------------
[INFO] Building quakers_app 1.0.1
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[DEBUG] Resolving plugin prefix quarkus from [org.apache.maven.plugins, org.codehaus.mojo]
[DEBUG] Resolved plugin prefix quarkus to io.quarkus.platform:quarkus-maven-plugin from POM com.mossplay.games:quakers_app:jar:1.0.1
[DEBUG] Lifecycle clean -> [pre-clean, clean, post-clean]
[DEBUG] Lifecycle default -> [validate, initialize, generate-sources, process-sources, generate-resources, process-resources, compile, process-classes, generate-test-sources, process-test-sources, generate-test-resources, process-test-resources, test-compile, process-test-classes, test, prepare-package, package, pre-integration-test, integration-test, post-integration-test, verify, install, deploy]
[DEBUG] Lifecycle site -> [pre-site, site, post-site, site-deploy]
[DEBUG] === PROJECT BUILD PLAN ================================================
[DEBUG] Project:       com.mossplay.games:quakers_app:1.0.1
[DEBUG] Dependencies (collect): []
[DEBUG] Dependencies (resolve): [test]
[DEBUG] Repositories (dependencies): [central (https://repo.maven.apache.org/maven2, default, releases)]
[DEBUG] Repositories (plugins)     : [central (https://repo.maven.apache.org/maven2, default, releases)]
[DEBUG] -----------------------------------------------------------------------
[DEBUG] Goal:          io.quarkus.platform:quarkus-maven-plugin:3.16.2:dev (default-cli)
[DEBUG] Style:         Regular
[DEBUG] Configuration: <?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <argsString default-value="${quarkus.args}"/>
  <buildDir default-value="${project.build.directory}"/>
  <debug default-value="${debug}"/>
  <debugHost default-value="${debugHost}"/>
  <debugPort default-value="${debugPort}"/>
  <deleteDevJar default-value="TRUE"/>
  <enforceBuildGoal default-value="${quarkus.enforceBuildGoal}"/>
  <forceC2 default-value="${forceC2}"/>
  <jvmArgs default-value="${jvm.args}"/>
  <modules default-value="${add-modules}"/>
  <mojoExecution default-value="${mojoExecution}"/>
  <noDeps default-value="${noDeps}"/>
  <openJavaLang default-value="${open-lang-package}"/>
  <outputDirectory default-value="${project.build.outputDirectory}"/>
  <pluginRepos default-value="${project.remotePluginRepositories}"/>
  <preventnoverify default-value="${preventnoverify}"/>
  <project default-value="${project}"/>
  <release default-value="${maven.compiler.release}"/>
  <repoSession default-value="${repositorySystemSession}"/>
  <repos default-value="${project.remoteProjectRepositories}"/>
  <session default-value="${session}"/>
  <skipPlugins default-value="org.codehaus.mojo:flatten-maven-plugin"/>
  <source default-value="${maven.compiler.source}"/>
  <sourceDir default-value="${project.build.sourceDirectory}"/>
  <suspend default-value="${suspend}"/>
  <target default-value="${maven.compiler.target}"/>
  <warnIfBuildGoalMissing>${quarkus.warnIfBuildGoalMissing}</warnIfBuildGoalMissing>
</configuration>
[DEBUG] =======================================================================
[DEBUG] Dependency collection stats {ConflictMarker.analyzeTime=1982833, ConflictMarker.markTime=408958, ConflictMarker.nodeCount=865, ConflictIdSorter.graphTime=1594084, ConflictIdSorter.topsortTime=129166, ConflictIdSorter.conflictIdCount=177, ConflictIdSorter.conflictIdCycleCount=0, ConflictResolver.totalTime=21640042, ConflictResolver.conflictItemCount=445, DfDependencyCollector.collectTime=735777541, DfDependencyCollector.transformTime=25875709}
[DEBUG] com.mossplay.games:quakers_app:jar:1.0.1
[DEBUG]    io.quarkus:quarkus-arc:jar:3.16.2:compile
[DEBUG]       io.quarkus.arc:arc:jar:3.16.2:compile (version managed from 3.16.2)
[DEBUG]          jakarta.transaction:jakarta.transaction-api:jar:2.0.1:compile (version managed from 2.0.1)
[DEBUG]       io.quarkus:quarkus-core:jar:3.16.2:compile (version managed from 3.16.2)
[DEBUG]          jakarta.inject:jakarta.inject-api:jar:2.0.1:compile (version managed from 2.0.1)
[DEBUG]          io.smallrye.common:smallrye-common-os:jar:2.7.0:compile (version managed from 2.7.0)
[DEBUG]          io.quarkus:quarkus-ide-launcher:jar:3.16.2:compile (version managed from 3.16.2) (exclusions managed from [*:*:*:*])
[DEBUG]          io.quarkus:quarkus-development-mode-spi:jar:3.16.2:compile (version managed from 3.16.2)
[DEBUG]          io.smallrye.config:smallrye-config:jar:3.9.1:compile (version managed from 3.9.1) (exclusions managed from [javax.enterprise:cdi-api:*:*, javax.annotation:javax.annotation-api:*:*])
[DEBUG]             io.smallrye.config:smallrye-config-core:jar:3.9.1:compile (version managed from 3.9.1)
[DEBUG]                io.smallrye.common:smallrye-common-classloader:jar:2.7.0:compile (version managed from 2.4.0)
[DEBUG]                io.smallrye.config:smallrye-config-common:jar:3.9.1:compile (version managed from 3.9.1)
[DEBUG]          org.jboss.logmanager:jboss-logmanager:jar:3.0.6.Final:compile (version managed from 3.0.6.Final) (exclusions managed from [org.jboss.modules:jboss-modules:*:*, org.eclipse.parsson:jakarta.json:*:*])
[DEBUG]             io.smallrye.common:smallrye-common-constraint:jar:2.7.0:compile (version managed from 2.2.0)
[DEBUG]             io.smallrye.common:smallrye-common-cpu:jar:2.7.0:compile (version managed from 2.2.0)
[DEBUG]             io.smallrye.common:smallrye-common-expression:jar:2.7.0:compile (version managed from 2.2.0)
[DEBUG]             io.smallrye.common:smallrye-common-net:jar:2.7.0:compile (version managed from 2.2.0)
[DEBUG]             io.smallrye.common:smallrye-common-ref:jar:2.7.0:compile (version managed from 2.2.0)
[DEBUG]          org.jboss.logging:jboss-logging-annotations:jar:3.0.2.Final:compile (version managed from 3.0.2.Final)
[DEBUG]          org.jboss.threads:jboss-threads:jar:3.8.0.Final:compile (version managed from 3.8.0.Final)
[DEBUG]             io.smallrye.common:smallrye-common-function:jar:2.7.0:compile (version managed from 2.6.0)
[DEBUG]          org.jboss.slf4j:slf4j-jboss-logmanager:jar:2.0.0.Final:compile (version managed from 2.0.0.Final)
[DEBUG]          org.wildfly.common:wildfly-common:jar:1.7.0.Final:compile (version managed from 1.7.0.Final)
[DEBUG]          io.quarkus:quarkus-bootstrap-runner:jar:3.16.2:compile (version managed from 3.16.2)
[DEBUG]             io.quarkus:quarkus-classloader-commons:jar:3.16.2:compile (version managed from 3.16.2)
[DEBUG]             io.smallrye.common:smallrye-common-io:jar:2.7.0:compile (version managed from 2.7.0)
[DEBUG]          io.quarkus:quarkus-fs-util:jar:0.0.10:compile (version managed from 0.0.10)
[DEBUG]       org.eclipse.microprofile.context-propagation:microprofile-context-propagation-api:jar:1.3:compile (version managed from 1.3)
[DEBUG]    io.quarkus:quarkus-rest:jar:3.16.2:compile
[DEBUG]       io.quarkus:quarkus-rest-common:jar:3.16.2:compile (version managed from 3.16.2)
[DEBUG]          io.quarkus.resteasy.reactive:resteasy-reactive-common:jar:3.16.2:compile (version managed from 3.16.2)
[DEBUG]             io.quarkus.resteasy.reactive:resteasy-reactive-common-types:jar:3.16.2:compile (version managed from 3.16.2)
[DEBUG]          io.quarkus:quarkus-vertx:jar:3.16.2:compile (version managed from 3.16.2)
[DEBUG]             io.quarkus:quarkus-netty:jar:3.16.2:compile (version managed from 3.16.2)
[DEBUG]                io.netty:netty-codec:jar:4.1.111.Final:compile (version managed from 4.1.111.Final)
[DEBUG]             io.netty:netty-codec-haproxy:jar:4.1.111.Final:compile (version managed from 4.1.111.Final)
[DEBUG]             io.quarkus:quarkus-vertx-latebound-mdc-provider:jar:3.16.2:compile (version managed from 3.16.2)
[DEBUG]             io.smallrye:smallrye-fault-tolerance-vertx:jar:6.5.0:compile (version managed from 6.5.0)
[DEBUG]       io.quarkus.resteasy.reactive:resteasy-reactive-vertx:jar:3.16.2:compile (version managed from 3.16.2)
[DEBUG]          io.vertx:vertx-web:jar:4.5.10:compile (version managed from 4.5.9)
[DEBUG]             io.vertx:vertx-web-common:jar:4.5.10:compile (version managed from 4.5.10)
[DEBUG]             io.vertx:vertx-auth-common:jar:4.5.10:compile (version managed from 4.5.10)
[DEBUG]             io.vertx:vertx-bridge-common:jar:4.5.10:compile (version managed from 4.5.10)
[DEBUG]          io.smallrye.reactive:smallrye-mutiny-vertx-core:jar:3.15.0:compile (version managed from 3.13.2) (exclusions managed from [])
[DEBUG]             io.smallrye.reactive:smallrye-mutiny-vertx-runtime:jar:3.15.0:compile (version managed from 3.15.0)
[DEBUG]             io.smallrye.reactive:vertx-mutiny-generator:jar:3.15.0:compile (version managed from 3.15.0)
[DEBUG]                io.vertx:vertx-codegen:jar:4.5.10:compile (version managed from 4.5.10)
[DEBUG]          io.quarkus.resteasy.reactive:resteasy-reactive:jar:3.16.2:compile (version managed from 3.16.2)
[DEBUG]          io.quarkus.vertx.utils:quarkus-vertx-utils:jar:3.16.2:compile (version managed from 3.16.2)
[DEBUG]          jakarta.ws.rs:jakarta.ws.rs-api:jar:3.1.0:compile (version managed from 3.1.0)
[DEBUG]          jakarta.xml.bind:jakarta.xml.bind-api:jar:4.0.2:compile (version managed from 4.0.2)
[DEBUG]             jakarta.activation:jakarta.activation-api:jar:2.1.3:compile (version managed from 2.1.3)
[DEBUG]       io.quarkus:quarkus-vertx-http:jar:3.16.2:compile (version managed from 3.16.2)
[DEBUG]          io.quarkus:quarkus-security-runtime-spi:jar:3.16.2:compile (version managed from 3.16.2)
[DEBUG]          io.quarkus:quarkus-tls-registry:jar:3.16.2:compile (version managed from 3.16.2)
[DEBUG]          io.smallrye.common:smallrye-common-vertx-context:jar:2.7.0:compile (version managed from 2.7.0)
[DEBUG]          io.quarkus.security:quarkus-security:jar:2.1.0:compile (version managed from 2.1.0) (exclusions managed from [javax.enterprise:cdi-api:*:*])
[DEBUG]          io.smallrye.reactive:smallrye-mutiny-vertx-web:jar:3.15.0:compile (version managed from 3.15.0)
[DEBUG]             io.smallrye.reactive:smallrye-mutiny-vertx-web-common:jar:3.15.0:compile (version managed from 3.15.0)
[DEBUG]             io.smallrye.reactive:smallrye-mutiny-vertx-auth-common:jar:3.15.0:compile (version managed from 3.15.0)
[DEBUG]             io.smallrye.reactive:smallrye-mutiny-vertx-bridge-common:jar:3.15.0:compile (version managed from 3.15.0)
[DEBUG]             io.smallrye.reactive:smallrye-mutiny-vertx-uri-template:jar:3.15.0:compile (version managed from 3.15.0)
[DEBUG]                io.vertx:vertx-uri-template:jar:4.5.10:compile (version managed from 4.5.10)
[DEBUG]          io.github.crac:org-crac:jar:0.1.3:compile (version managed from 0.1.3)
[DEBUG]          com.aayushatharva.brotli4j:brotli4j:jar:1.16.0:compile (version managed from 1.16.0)
[DEBUG]             com.aayushatharva.brotli4j:service:jar:1.16.0:compile
[DEBUG]             com.aayushatharva.brotli4j:native-osx-x86_64:jar:1.16.0:compile
[DEBUG]       io.quarkus:quarkus-jsonp:jar:3.16.2:compile (version managed from 3.16.2)
[DEBUG]          org.eclipse.parsson:parsson:jar:1.1.7:compile (version managed from 1.1.7)
[DEBUG]       io.quarkus:quarkus-virtual-threads:jar:3.16.2:compile (version managed from 3.16.2)
[DEBUG]          io.vertx:vertx-core:jar:4.5.10:compile (version managed from 4.5.10)
[DEBUG]             io.netty:netty-common:jar:4.1.111.Final:compile (version managed from 4.1.111.Final)
[DEBUG]             io.netty:netty-buffer:jar:4.1.111.Final:compile (version managed from 4.1.111.Final)
[DEBUG]             io.netty:netty-transport:jar:4.1.111.Final:compile (version managed from 4.1.111.Final)
[DEBUG]             io.netty:netty-handler:jar:4.1.111.Final:compile (version managed from 4.1.111.Final)
[DEBUG]                io.netty:netty-transport-native-unix-common:jar:4.1.111.Final:compile (version managed from 4.1.111.Final)
[DEBUG]             io.netty:netty-handler-proxy:jar:4.1.111.Final:compile (version managed from 4.1.111.Final)
[DEBUG]                io.netty:netty-codec-socks:jar:4.1.111.Final:compile (version managed from 4.1.111.Final)
[DEBUG]             io.netty:netty-codec-http:jar:4.1.111.Final:compile (version managed from 4.1.111.Final)
[DEBUG]             io.netty:netty-codec-http2:jar:4.1.111.Final:compile (version managed from 4.1.111.Final)
[DEBUG]             io.netty:netty-resolver:jar:4.1.111.Final:compile (version managed from 4.1.111.Final)
[DEBUG]             io.netty:netty-resolver-dns:jar:4.1.111.Final:compile (version managed from 4.1.111.Final)
[DEBUG]                io.netty:netty-codec-dns:jar:4.1.111.Final:compile (version managed from 4.1.111.Final)
[DEBUG]             com.fasterxml.jackson.core:jackson-core:jar:2.18.1:compile (version managed from 2.16.1)
[DEBUG]    io.quarkus:quarkus-rest-jackson:jar:3.16.2:compile
[DEBUG]       io.quarkus:quarkus-rest-jackson-common:jar:3.16.2:compile (version managed from 3.16.2)
[DEBUG]          io.quarkus.resteasy.reactive:resteasy-reactive-jackson:jar:3.16.2:compile (version managed from 3.16.2)
[DEBUG]             com.fasterxml.jackson.core:jackson-databind:jar:2.18.1:compile (version managed from 2.18.1)
[DEBUG]                com.fasterxml.jackson.core:jackson-annotations:jar:2.18.1:compile (version managed from 2.18.1)
[DEBUG]          io.quarkus:quarkus-jackson:jar:3.16.2:compile (version managed from 3.16.2)
[DEBUG]             com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.18.1:compile (version managed from 2.18.1)
[DEBUG]             com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.18.1:compile (version managed from 2.18.1)
[DEBUG]             com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.18.1:compile (version managed from 2.18.1)
[DEBUG]    io.quarkus:quarkus-jdbc-mysql:jar:3.16.2:compile
[DEBUG]       com.mysql:mysql-connector-j:jar:8.3.0:compile (version managed from 8.3.0) (exclusions managed from [com.google.protobuf:protobuf-java:*:*])
[DEBUG]    org.codehaus.jettison:jettison:jar:1.5.4:compile
[DEBUG]    com.google.code.gson:gson:jar:2.13.1:compile
[DEBUG]       com.google.errorprone:error_prone_annotations:jar:2.33.0:compile (version managed from 2.38.0) (exclusions managed from [])
[DEBUG]    jakarta.json.bind:jakarta.json.bind-api:jar:3.0.1:compile
[DEBUG]    org.projectlombok:lombok:jar:1.18.24:compile
[DEBUG]    redis.clients:jedis:jar:5.1.5:compile
[DEBUG]       org.slf4j:slf4j-api:jar:2.0.6:compile (version managed from 1.7.36)
[DEBUG]       org.apache.commons:commons-pool2:jar:2.12.0:compile
[DEBUG]       org.json:json:jar:20231013:compile
[DEBUG]    org.apache.httpcomponents:fluent-hc:jar:4.5.14:compile
[DEBUG]       org.apache.httpcomponents:httpclient:jar:4.5.14:compile (version managed from 4.5.14) (exclusions managed from [])
[DEBUG]          org.apache.httpcomponents:httpcore:jar:4.4.16:compile (version managed from 4.4.16)
[DEBUG]          commons-codec:commons-codec:jar:1.17.1:compile (version managed from 1.11)
[DEBUG]       commons-logging:commons-logging:jar:1.2:compile
[DEBUG]    joda-time:joda-time:jar:2.8.1:compile
[DEBUG]    org.apache.commons:commons-lang3:jar:3.17.0:compile
[DEBUG]    net.sf.uadetector:uadetector-resources:jar:2014.10:compile
[DEBUG]    net.sf.uadetector:uadetector-core:jar:0.9.22:compile
[DEBUG]       net.sf.qualitycheck:quality-check:jar:1.3:compile
[DEBUG]       com.google.code.findbugs:jsr305:jar:3.0.2:compile (version managed from 2.0.3)
[DEBUG]       javax.annotation:jsr250-api:jar:1.0:compile
[DEBUG]    io.smallrye.reactive:smallrye-reactive-messaging-api:jar:4.25.0:compile
[DEBUG]       io.smallrye.common:smallrye-common-annotation:jar:2.7.0:compile (version managed from 2.6.0)
[DEBUG]       jakarta.enterprise:jakarta.enterprise.cdi-api:jar:4.1.0:compile (version managed from 4.0.1)
[DEBUG]          jakarta.enterprise:jakarta.enterprise.lang-model:jar:4.1.0:compile
[DEBUG]          jakarta.el:jakarta.el-api:jar:5.0.1:compile (version managed from 6.0.0)
[DEBUG]          jakarta.interceptor:jakarta.interceptor-api:jar:2.2.0:compile (version managed from 2.2.0) (exclusions managed from [jakarta.annotation:jakarta.annotation-api:*:*])
[DEBUG]       io.opentelemetry:opentelemetry-api:jar:1.42.1:compile (version managed from 1.39.0)
[DEBUG]          io.opentelemetry:opentelemetry-context:jar:1.42.1:compile (version managed from 1.42.1)
[DEBUG]       org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile (version managed from 3.6.1.Final)
[DEBUG]       io.smallrye.reactive:smallrye-reactive-converter-api:jar:3.0.1:compile (version managed from 3.0.0)
[DEBUG]          org.reactivestreams:reactive-streams:jar:1.0.4:compile (version managed from 1.0.4) (exclusions managed from [])
[DEBUG]       io.smallrye.reactive:mutiny:jar:2.6.2:compile (version managed from 2.6.1)
[DEBUG]          org.jctools:jctools-core:jar:4.0.5:compile (version managed from 4.0.5)
[DEBUG]       io.smallrye.reactive:mutiny-zero:jar:1.1.0:compile (version managed from 1.1.0)
[DEBUG]       io.smallrye.reactive:mutiny-zero-flow-adapters:jar:1.1.0:compile (version managed from 1.1.0)
[DEBUG]    org.eclipse.microprofile.health:microprofile-health-api:jar:4.0.1:compile
[DEBUG]    io.quarkus:quarkus-agroal:jar:3.16.2:compile
[DEBUG]       io.quarkus:quarkus-datasource:jar:3.16.2:compile (version managed from 3.16.2)
[DEBUG]          io.quarkus:quarkus-datasource-common:jar:3.16.2:compile (version managed from 3.16.2)
[DEBUG]       io.quarkus:quarkus-narayana-jta:jar:3.16.2:compile (version managed from 3.16.2)
[DEBUG]          io.quarkus:quarkus-transaction-annotations:jar:3.16.2:compile (version managed from 3.16.2)
[DEBUG]          io.smallrye:smallrye-context-propagation-jta:jar:2.1.2:compile (version managed from 2.1.2)
[DEBUG]          io.smallrye.reactive:smallrye-reactive-converter-mutiny:jar:3.0.1:compile (version managed from 3.0.1)
[DEBUG]          org.jboss.narayana.jta:narayana-jta:jar:7.0.2.Final:compile (version managed from 7.0.2.Final)
[DEBUG]             jakarta.resource:jakarta.resource-api:jar:2.1.0:compile (version managed from 2.0.0)
[DEBUG]             org.jboss.invocation:jboss-invocation:jar:2.0.0.Final:compile
[DEBUG]             org.eclipse.microprofile.reactive-streams-operators:microprofile-reactive-streams-operators-api:jar:3.0.1:compile (version managed from 3.0) (exclusions managed from [])
[DEBUG]          org.jboss.narayana.jts:narayana-jts-integration:jar:7.0.2.Final:compile (version managed from 7.0.2.Final)
[DEBUG]       io.agroal:agroal-api:jar:2.5:compile (version managed from 2.5)
[DEBUG]       io.agroal:agroal-narayana:jar:2.5:compile (version managed from 2.5)
[DEBUG]          org.jboss:jboss-transaction-spi:jar:8.0.0.Final:compile (version managed from 8.0.0.Final)
[DEBUG]       io.agroal:agroal-pool:jar:2.5:compile (version managed from 2.5)
[DEBUG]       io.quarkus:quarkus-credentials:jar:3.16.2:compile (version managed from 3.16.2)
[DEBUG]    javax.inject:javax.inject:jar:1:compile
[DEBUG]    io.quarkus:quarkus-smallrye-fault-tolerance:jar:3.16.2:compile
[DEBUG]       io.quarkus:quarkus-mutiny:jar:3.16.2:compile (version managed from 3.16.2)
[DEBUG]          io.smallrye.reactive:mutiny-smallrye-context-propagation:jar:2.6.2:compile (version managed from 2.6.2)
[DEBUG]       io.smallrye:smallrye-fault-tolerance:jar:6.5.0:compile (version managed from 6.5.0)
[DEBUG]          org.eclipse.microprofile.fault-tolerance:microprofile-fault-tolerance-api:jar:4.1.1:compile (version managed from 4.1.1)
[DEBUG]          io.smallrye:smallrye-fault-tolerance-api:jar:6.5.0:compile (version managed from 6.5.0)
[DEBUG]          io.smallrye:smallrye-fault-tolerance-core:jar:6.5.0:compile (version managed from 6.5.0)
[DEBUG]          io.smallrye:smallrye-fault-tolerance-autoconfig-core:jar:6.5.0:compile (version managed from 6.5.0)
[DEBUG]          org.eclipse.microprofile.config:microprofile-config-api:jar:3.1:compile (version managed from 3.1) (exclusions managed from [])
[DEBUG]       jakarta.annotation:jakarta.annotation-api:jar:3.0.0:compile (version managed from 3.0.0)
[DEBUG]       org.jboss.logging:commons-logging-jboss-logging:jar:1.0.0.Final:compile (version managed from 1.0.0.Final)
[DEBUG]       io.quarkus:quarkus-smallrye-context-propagation:jar:3.16.2:compile (version managed from 3.16.2)
[DEBUG]          io.smallrye:smallrye-context-propagation:jar:2.1.2:compile (version managed from 2.1.2)
[DEBUG]             io.smallrye:smallrye-context-propagation-api:jar:2.1.2:compile (version managed from 2.1.2)
[DEBUG]             io.smallrye:smallrye-context-propagation-storage:jar:2.1.2:compile (version managed from 2.1.2)
[DEBUG]       io.smallrye:smallrye-fault-tolerance-context-propagation:jar:6.5.0:compile (version managed from 6.5.0)
[DEBUG]       io.smallrye:smallrye-fault-tolerance-mutiny:jar:6.5.0:compile (version managed from 6.5.0)
[DEBUG]    com.rabbitmq:amqp-client:jar:5.25.0:compile
[DEBUG]    jakarta.validation:jakarta.validation-api:jar:3.1.1:compile
[DEBUG]    io.quarkus:quarkus-container-image-jib:jar:3.16.2:compile
[DEBUG]       io.quarkus:quarkus-container-image:jar:3.16.2:compile (version managed from 3.16.2)
[DEBUG]    io.quarkus:quarkus-smallrye-health:jar:3.16.2:compile
[DEBUG]       io.smallrye:smallrye-health:jar:4.1.0:compile (version managed from 4.1.0)
[DEBUG]          io.smallrye:smallrye-health-api:jar:4.1.0:compile (version managed from 4.1.0)
[DEBUG]          jakarta.json:jakarta.json-api:jar:2.1.3:compile (version managed from 2.1.1)
[DEBUG]       io.smallrye:smallrye-health-provided-checks:jar:4.1.0:compile (version managed from 4.1.0)
[INFO] 
[INFO] --- quarkus:3.16.2:dev (default-cli) @ quakers_app ---
[DEBUG] Dependency collection stats {ConflictMarker.analyzeTime=1123000, ConflictMarker.markTime=217042, ConflictMarker.nodeCount=1077, ConflictIdSorter.graphTime=230750, ConflictIdSorter.topsortTime=439167, ConflictIdSorter.conflictIdCount=150, ConflictIdSorter.conflictIdCycleCount=0, ConflictResolver.totalTime=7596541, ConflictResolver.conflictItemCount=336, DfDependencyCollector.collectTime=29884583, DfDependencyCollector.transformTime=10064083}
[DEBUG] io.quarkus.platform:quarkus-maven-plugin:jar:3.16.2
[DEBUG]    io.quarkus:quarkus-bootstrap-core:jar:3.16.2:compile
[DEBUG]       io.quarkus:quarkus-classloader-commons:jar:3.16.2:compile (version managed from default)
[DEBUG]       io.quarkus:quarkus-bootstrap-app-model:jar:3.16.2:compile (version managed from default)
[DEBUG]          org.jboss.logging:commons-logging-jboss-logging:jar:1.0.0.Final:runtime (version managed from default)
[DEBUG]       io.smallrye.common:smallrye-common-io:jar:2.7.0:compile (version managed from default)
[DEBUG]    io.quarkus:quarkus-bootstrap-maven-resolver:jar:3.16.2:compile
[DEBUG]       io.smallrye.beanbag:smallrye-beanbag-maven:jar:1.5.2:compile (version managed from default) (exclusions managed from default)
[DEBUG]          io.smallrye.beanbag:smallrye-beanbag-sisu:jar:1.5.2:compile
[DEBUG]             io.smallrye.beanbag:smallrye-beanbag:jar:1.5.2:compile
[DEBUG]          io.smallrye.common:smallrye-common-constraint:jar:2.7.0:compile (version managed from default)
[DEBUG]          commons-codec:commons-codec:jar:1.17.1:compile (version managed from default)
[DEBUG]          org.apache.httpcomponents:httpclient:jar:4.5.14:compile (version managed from default) (exclusions managed from default)
[DEBUG]          org.apache.httpcomponents:httpcore:jar:4.4.16:compile (version managed from default)
[DEBUG]          org.apache.maven:maven-artifact:jar:3.9.9:compile (version managed from default)
[DEBUG]          org.apache.maven:maven-builder-support:jar:3.9.9:compile (version managed from default)
[DEBUG]          org.apache.maven:maven-model:jar:3.9.9:compile (version managed from default)
[DEBUG]          org.apache.maven:maven-model-builder:jar:3.9.9:compile (version managed from default) (exclusions managed from default)
[DEBUG]          org.apache.maven:maven-repository-metadata:jar:3.9.9:compile (version managed from default)
[DEBUG]          org.apache.maven:maven-settings:jar:3.9.9:compile (version managed from default)
[DEBUG]          org.codehaus.plexus:plexus-interpolation:jar:1.26:compile (version managed from default)
[DEBUG]          org.codehaus.plexus:plexus-xml:jar:4.0.1:compile
[DEBUG]             org.apache.maven:maven-xml-impl:jar:4.0.0-alpha-5:compile
[DEBUG]                org.apache.maven:maven-api-xml:jar:4.0.0-alpha-5:compile
[DEBUG]                   org.apache.maven:maven-api-meta:jar:4.0.0-alpha-5:compile
[DEBUG]          org.codehaus.plexus:plexus-cipher:jar:2.0:compile (version managed from default)
[DEBUG]          org.codehaus.plexus:plexus-sec-dispatcher:jar:2.0:compile (version managed from default)
[DEBUG]       io.quarkus:quarkus-fs-util:jar:0.0.10:compile (version managed from default)
[DEBUG]       org.jboss.logmanager:jboss-logmanager:jar:3.0.6.Final:compile (version managed from default) (exclusions managed from default)
[DEBUG]          io.smallrye.common:smallrye-common-cpu:jar:2.7.0:compile (version managed from default)
[DEBUG]          io.smallrye.common:smallrye-common-expression:jar:2.7.0:compile (version managed from default)
[DEBUG]             io.smallrye.common:smallrye-common-function:jar:2.7.0:compile (version managed from default)
[DEBUG]          io.smallrye.common:smallrye-common-net:jar:2.7.0:compile (version managed from default)
[DEBUG]          io.smallrye.common:smallrye-common-ref:jar:2.7.0:compile (version managed from default)
[DEBUG]       org.jboss.logging:jboss-logging:jar:3.6.1.Final:compile (version managed from default)
[DEBUG]       org.slf4j:slf4j-api:jar:2.0.6:compile (version managed from default)
[DEBUG]       org.apache.maven:maven-embedder:jar:3.9.9:compile (version managed from default) (exclusions managed from default)
[DEBUG]          org.apache.maven.shared:maven-shared-utils:jar:3.4.2:compile (version managed from default)
[DEBUG]          com.google.inject:guice:jar:5.1.0:compile
[DEBUG]             aopalliance:aopalliance:jar:1.0:compile
[DEBUG]          com.google.guava:guava:jar:33.3.1-jre:compile (version managed from default) (exclusions managed from default)
[DEBUG]          com.google.guava:failureaccess:jar:1.0.1:compile (version managed from default)
[DEBUG]          javax.annotation:javax.annotation-api:jar:1.3.2:compile (version managed from default)
[DEBUG]          org.codehaus.plexus:plexus-classworlds:jar:2.6.0:compile (version managed from default) (exclusions managed from default)
[DEBUG]          commons-cli:commons-cli:jar:1.8.0:compile
[DEBUG]       org.eclipse.sisu:org.eclipse.sisu.plexus:jar:0.9.0.M3:compile (version managed from default) (exclusions managed from default)
[DEBUG]       org.apache.maven:maven-settings-builder:jar:3.9.9:compile (version managed from default) (exclusions managed from default)
[DEBUG]       org.apache.maven:maven-resolver-provider:jar:3.9.9:compile (version managed from default) (exclusions managed from default)
[DEBUG]    io.quarkus:quarkus-core-deployment:jar:3.16.2:compile
[DEBUG]       org.aesh:readline:jar:2.6:compile (version managed from default)
[DEBUG]          org.fusesource.jansi:jansi:jar:2.4.0:compile (version managed from default)
[DEBUG]       org.aesh:aesh:jar:2.8.2:compile (version managed from default)
[DEBUG]       org.apache.commons:commons-lang3:jar:3.17.0:compile (version managed from default)
[DEBUG]       org.wildfly.common:wildfly-common:jar:1.7.0.Final:compile (version managed from default)
[DEBUG]       io.quarkus.gizmo:gizmo:jar:1.8.0:compile (version managed from default)
[DEBUG]          org.ow2.asm:asm-util:jar:9.7.1:compile (version managed from default)
[DEBUG]             org.ow2.asm:asm-analysis:jar:9.7.1:compile (version managed from default)
[DEBUG]       io.smallrye:jandex:jar:3.2.3:compile (version managed from default)
[DEBUG]       org.ow2.asm:asm:jar:9.7.1:compile (version managed from default)
[DEBUG]       org.ow2.asm:asm-commons:jar:9.7.1:compile (version managed from default)
[DEBUG]          org.ow2.asm:asm-tree:jar:9.7.1:compile (version managed from default)
[DEBUG]       io.quarkus:quarkus-development-mode-spi:jar:3.16.2:compile (version managed from default)
[DEBUG]       io.quarkus:quarkus-hibernate-validator-spi:jar:3.16.2:compile (version managed from default)
[DEBUG]       io.quarkus:quarkus-class-change-agent:jar:3.16.2:compile (version managed from default)
[DEBUG]       io.quarkus:quarkus-devtools-utilities:jar:3.16.2:compile (version managed from default)
[DEBUG]       org.eclipse.sisu:org.eclipse.sisu.inject:jar:0.9.0.M3:compile (version managed from default)
[DEBUG]       io.quarkus:quarkus-core:jar:3.16.2:compile (version managed from default)
[DEBUG]          jakarta.annotation:jakarta.annotation-api:jar:3.0.0:compile (version managed from default)
[DEBUG]          jakarta.inject:jakarta.inject-api:jar:2.0.1:compile (version managed from default)
[DEBUG]          io.smallrye.config:smallrye-config:jar:3.9.1:compile (version managed from default) (exclusions managed from default)
[DEBUG]             io.smallrye.config:smallrye-config-core:jar:3.9.1:compile (version managed from default)
[DEBUG]                org.eclipse.microprofile.config:microprofile-config-api:jar:3.1:compile (version managed from default) (exclusions managed from default)
[DEBUG]                io.smallrye.common:smallrye-common-classloader:jar:2.7.0:compile (version managed from default)
[DEBUG]                io.smallrye.config:smallrye-config-common:jar:3.9.1:compile (version managed from default)
[DEBUG]          org.jboss.logging:jboss-logging-annotations:jar:3.0.2.Final:compile (version managed from default)
[DEBUG]          org.jboss.threads:jboss-threads:jar:3.8.0.Final:compile (version managed from default)
[DEBUG]             io.smallrye.common:smallrye-common-annotation:jar:2.7.0:compile (version managed from default)
[DEBUG]          io.quarkus:quarkus-bootstrap-runner:jar:3.16.2:compile (version managed from default)
[DEBUG]             io.github.crac:org-crac:jar:0.1.3:compile (version managed from default)
[DEBUG]       io.quarkus:quarkus-builder:jar:3.16.2:compile (version managed from default)
[DEBUG]       org.graalvm.sdk:nativeimage:jar:23.1.2:compile (version managed from default)
[DEBUG]          org.graalvm.sdk:word:jar:23.1.2:compile
[DEBUG]       org.junit.platform:junit-platform-launcher:jar:1.10.5:compile (version managed from default)
[DEBUG]          org.junit.platform:junit-platform-engine:jar:1.10.5:compile (version managed from default)
[DEBUG]             org.opentest4j:opentest4j:jar:1.3.0:compile
[DEBUG]             org.junit.platform:junit-platform-commons:jar:1.10.5:compile (version managed from default)
[DEBUG]          org.apiguardian:apiguardian-api:jar:1.1.2:compile
[DEBUG]       org.junit.jupiter:junit-jupiter:jar:5.10.5:compile (version managed from default)
[DEBUG]          org.junit.jupiter:junit-jupiter-api:jar:5.10.5:compile (version managed from default)
[DEBUG]          org.junit.jupiter:junit-jupiter-params:jar:5.10.5:compile (version managed from default)
[DEBUG]          org.junit.jupiter:junit-jupiter-engine:jar:5.10.5:runtime (version managed from default)
[DEBUG]    io.quarkus:quarkus-project-core-extension-codestarts:jar:3.16.2:compile
[DEBUG]    io.quarkus:quarkus-devtools-common:jar:3.16.2:compile
[DEBUG]       io.quarkus:quarkus-devtools-registry-client:jar:3.16.2:compile (version managed from default)
[DEBUG]       io.quarkus:quarkus-devtools-base-codestarts:jar:3.16.2:compile
[DEBUG]       io.quarkus:quarkus-devtools-codestarts:jar:3.16.2:compile
[DEBUG]          io.quarkus.qute:qute-core:jar:3.16.2:compile (version managed from default)
[DEBUG]             io.smallrye.reactive:mutiny:jar:2.6.2:compile (version managed from default)
[DEBUG]                org.jctools:jctools-core:jar:4.0.5:compile (version managed from default)
[DEBUG]          commons-io:commons-io:jar:2.17.0:compile (version managed from default)
[DEBUG]       org.apache.commons:commons-compress:jar:1.27.1:compile (version managed from default)
[DEBUG]       io.smallrye.common:smallrye-common-version:jar:2.7.0:compile (version managed from default)
[DEBUG]       io.smallrye.common:smallrye-common-os:jar:2.7.0:compile (version managed from default)
[DEBUG]       io.fabric8:maven-model-helper:jar:37:compile
[DEBUG]          org.jdom:jdom2:jar:2.0.6.1:compile
[DEBUG]       com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.18.1:compile (version managed from default)
[DEBUG]          org.yaml:snakeyaml:jar:2.3:compile (version managed from default)
[DEBUG]       com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.18.1:compile (version managed from default)
[DEBUG]       org.apache.maven:maven-plugin-api:jar:3.9.9:compile (version managed from default)
[DEBUG]       org.apache.maven:maven-core:jar:3.9.9:compile (version managed from default) (exclusions managed from default)
[DEBUG]          org.apache.maven.resolver:maven-resolver-impl:jar:1.9.22:compile (version managed from default)
[DEBUG]             org.apache.maven.resolver:maven-resolver-named-locks:jar:1.9.22:compile
[DEBUG]          org.apache.maven.resolver:maven-resolver-api:jar:1.9.22:compile (version managed from default)
[DEBUG]          org.apache.maven.resolver:maven-resolver-spi:jar:1.9.22:compile (version managed from default)
[DEBUG]          org.apache.maven.resolver:maven-resolver-util:jar:1.9.22:compile (version managed from default)
[DEBUG]          org.codehaus.plexus:plexus-component-annotations:jar:2.1.0:compile (version managed from default)
[DEBUG]       jakarta.enterprise:jakarta.enterprise.cdi-api:jar:4.1.0:compile (version managed from default)
[DEBUG]          jakarta.enterprise:jakarta.enterprise.lang-model:jar:4.1.0:compile
[DEBUG]          jakarta.el:jakarta.el-api:jar:5.0.1:compile (version managed from default)
[DEBUG]          jakarta.interceptor:jakarta.interceptor-api:jar:2.2.0:compile (version managed from default) (exclusions managed from default)
[DEBUG]       org.codejive:java-properties:jar:0.0.7:compile
[DEBUG]    io.quarkus:quarkus-analytics-common:jar:3.16.2:compile
[DEBUG]       com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.18.1:compile (version managed from default)
[DEBUG]       io.quarkus:quarkus-devtools-message-writer:jar:3.16.2:compile
[DEBUG]    io.quarkus:quarkus-cyclonedx-generator:jar:3.16.2:compile
[DEBUG]       org.cyclonedx:cyclonedx-core-java:jar:9.0.5:compile (version managed from default)
[DEBUG]          org.apache.commons:commons-collections4:jar:4.4:compile
[DEBUG]          com.github.package-url:packageurl-java:jar:1.5.0:compile
[DEBUG]          com.fasterxml.jackson.dataformat:jackson-dataformat-xml:jar:2.18.1:compile (version managed from default)
[DEBUG]             org.codehaus.woodstox:stax2-api:jar:4.2.2:compile
[DEBUG]             com.fasterxml.woodstox:woodstox-core:jar:7.0.0:compile
[DEBUG]          com.networknt:json-schema-validator:jar:1.5.1:compile
[DEBUG]             com.ethlo.time:itu:jar:1.10.2:compile
[DEBUG]    javax.inject:javax.inject:jar:1:compile
[DEBUG]    org.freemarker:freemarker:jar:2.3.33:compile
[DEBUG]    org.eclipse.parsson:parsson:jar:1.1.7:compile
[DEBUG]       jakarta.json:jakarta.json-api:jar:2.1.3:compile (version managed from default)
[DEBUG]    com.fasterxml.jackson.core:jackson-databind:jar:2.18.1:compile
[DEBUG]       com.fasterxml.jackson.core:jackson-annotations:jar:2.18.1:compile (version managed from default)
[DEBUG]       com.fasterxml.jackson.core:jackson-core:jar:2.18.1:compile (version managed from default)
[DEBUG]    org.twdata.maven:mojo-executor:jar:2.4.0:compile
[DEBUG]       org.codehaus.plexus:plexus-utils:jar:3.5.1:compile (version managed from default)
[DEBUG]    org.jboss.slf4j:slf4j-jboss-logmanager:jar:2.0.0.Final:compile
[DEBUG] Created new class realm plugin>io.quarkus.platform:quarkus-maven-plugin:3.16.2
[DEBUG] Importing foreign packages into class realm plugin>io.quarkus.platform:quarkus-maven-plugin:3.16.2
[DEBUG]   Imported:  < project>com.mossplay.games:quakers_app:1.0.1
[DEBUG] Populating class realm plugin>io.quarkus.platform:quarkus-maven-plugin:3.16.2
[DEBUG]   Included: io.quarkus.platform:quarkus-maven-plugin:jar:3.16.2
[DEBUG]   Included: io.quarkus:quarkus-bootstrap-core:jar:3.16.2
[DEBUG]   Included: io.quarkus:quarkus-classloader-commons:jar:3.16.2
[DEBUG]   Included: io.quarkus:quarkus-bootstrap-app-model:jar:3.16.2
[DEBUG]   Included: org.jboss.logging:commons-logging-jboss-logging:jar:1.0.0.Final
[DEBUG]   Included: io.smallrye.common:smallrye-common-io:jar:2.7.0
[DEBUG]   Included: io.quarkus:quarkus-bootstrap-maven-resolver:jar:3.16.2
[DEBUG]   Included: io.smallrye.beanbag:smallrye-beanbag-maven:jar:1.5.2
[DEBUG]   Included: io.smallrye.beanbag:smallrye-beanbag-sisu:jar:1.5.2
[DEBUG]   Included: io.smallrye.beanbag:smallrye-beanbag:jar:1.5.2
[DEBUG]   Included: io.smallrye.common:smallrye-common-constraint:jar:2.7.0
[DEBUG]   Included: commons-codec:commons-codec:jar:1.17.1
[DEBUG]   Included: org.apache.httpcomponents:httpclient:jar:4.5.14
[DEBUG]   Included: org.apache.httpcomponents:httpcore:jar:4.4.16
[DEBUG]   Included: org.apache.maven:maven-builder-support:jar:3.9.9
[DEBUG]   Included: org.codehaus.plexus:plexus-interpolation:jar:1.26
[DEBUG]   Included: org.codehaus.plexus:plexus-xml:jar:4.0.1
[DEBUG]   Included: org.apache.maven:maven-xml-impl:jar:4.0.0-alpha-5
[DEBUG]   Included: org.apache.maven:maven-api-xml:jar:4.0.0-alpha-5
[DEBUG]   Included: org.apache.maven:maven-api-meta:jar:4.0.0-alpha-5
[DEBUG]   Included: org.codehaus.plexus:plexus-cipher:jar:2.0
[DEBUG]   Included: org.codehaus.plexus:plexus-sec-dispatcher:jar:2.0
[DEBUG]   Included: io.quarkus:quarkus-fs-util:jar:0.0.10
[DEBUG]   Included: org.jboss.logmanager:jboss-logmanager:jar:3.0.6.Final
[DEBUG]   Included: io.smallrye.common:smallrye-common-cpu:jar:2.7.0
[DEBUG]   Included: io.smallrye.common:smallrye-common-expression:jar:2.7.0
[DEBUG]   Included: io.smallrye.common:smallrye-common-function:jar:2.7.0
[DEBUG]   Included: io.smallrye.common:smallrye-common-net:jar:2.7.0
[DEBUG]   Included: io.smallrye.common:smallrye-common-ref:jar:2.7.0
[DEBUG]   Included: org.jboss.logging:jboss-logging:jar:3.6.1.Final
[DEBUG]   Included: org.apache.maven:maven-embedder:jar:3.9.9
[DEBUG]   Included: org.apache.maven.shared:maven-shared-utils:jar:3.4.2
[DEBUG]   Included: com.google.inject:guice:jar:5.1.0
[DEBUG]   Included: aopalliance:aopalliance:jar:1.0
[DEBUG]   Included: com.google.guava:guava:jar:33.3.1-jre
[DEBUG]   Included: com.google.guava:failureaccess:jar:1.0.1
[DEBUG]   Included: commons-cli:commons-cli:jar:1.8.0
[DEBUG]   Included: io.quarkus:quarkus-core-deployment:jar:3.16.2
[DEBUG]   Included: org.aesh:readline:jar:2.6
[DEBUG]   Included: org.aesh:aesh:jar:2.8.2
[DEBUG]   Included: org.apache.commons:commons-lang3:jar:3.17.0
[DEBUG]   Included: org.wildfly.common:wildfly-common:jar:1.7.0.Final
[DEBUG]   Included: io.quarkus.gizmo:gizmo:jar:1.8.0
[DEBUG]   Included: org.ow2.asm:asm-util:jar:9.7.1
[DEBUG]   Included: org.ow2.asm:asm-analysis:jar:9.7.1
[DEBUG]   Included: io.smallrye:jandex:jar:3.2.3
[DEBUG]   Included: org.ow2.asm:asm:jar:9.7.1
[DEBUG]   Included: org.ow2.asm:asm-commons:jar:9.7.1
[DEBUG]   Included: org.ow2.asm:asm-tree:jar:9.7.1
[DEBUG]   Included: io.quarkus:quarkus-development-mode-spi:jar:3.16.2
[DEBUG]   Included: io.quarkus:quarkus-hibernate-validator-spi:jar:3.16.2
[DEBUG]   Included: io.quarkus:quarkus-class-change-agent:jar:3.16.2
[DEBUG]   Included: io.quarkus:quarkus-devtools-utilities:jar:3.16.2
[DEBUG]   Included: org.eclipse.sisu:org.eclipse.sisu.inject:jar:0.9.0.M3
[DEBUG]   Included: io.quarkus:quarkus-core:jar:3.16.2
[DEBUG]   Included: jakarta.annotation:jakarta.annotation-api:jar:3.0.0
[DEBUG]   Included: jakarta.inject:jakarta.inject-api:jar:2.0.1
[DEBUG]   Included: io.smallrye.config:smallrye-config:jar:3.9.1
[DEBUG]   Included: io.smallrye.config:smallrye-config-core:jar:3.9.1
[DEBUG]   Included: org.eclipse.microprofile.config:microprofile-config-api:jar:3.1
[DEBUG]   Included: io.smallrye.common:smallrye-common-classloader:jar:2.7.0
[DEBUG]   Included: io.smallrye.config:smallrye-config-common:jar:3.9.1
[DEBUG]   Included: org.jboss.logging:jboss-logging-annotations:jar:3.0.2.Final
[DEBUG]   Included: org.jboss.threads:jboss-threads:jar:3.8.0.Final
[DEBUG]   Included: io.smallrye.common:smallrye-common-annotation:jar:2.7.0
[DEBUG]   Included: io.quarkus:quarkus-bootstrap-runner:jar:3.16.2
[DEBUG]   Included: io.github.crac:org-crac:jar:0.1.3
[DEBUG]   Included: io.quarkus:quarkus-builder:jar:3.16.2
[DEBUG]   Included: org.graalvm.sdk:nativeimage:jar:23.1.2
[DEBUG]   Included: org.graalvm.sdk:word:jar:23.1.2
[DEBUG]   Included: org.junit.platform:junit-platform-launcher:jar:1.10.5
[DEBUG]   Included: org.junit.platform:junit-platform-engine:jar:1.10.5
[DEBUG]   Included: org.opentest4j:opentest4j:jar:1.3.0
[DEBUG]   Included: org.junit.platform:junit-platform-commons:jar:1.10.5
[DEBUG]   Included: org.apiguardian:apiguardian-api:jar:1.1.2
[DEBUG]   Included: org.junit.jupiter:junit-jupiter:jar:5.10.5
[DEBUG]   Included: org.junit.jupiter:junit-jupiter-api:jar:5.10.5
[DEBUG]   Included: org.junit.jupiter:junit-jupiter-params:jar:5.10.5
[DEBUG]   Included: org.junit.jupiter:junit-jupiter-engine:jar:5.10.5
[DEBUG]   Included: io.quarkus:quarkus-project-core-extension-codestarts:jar:3.16.2
[DEBUG]   Included: io.quarkus:quarkus-devtools-common:jar:3.16.2
[DEBUG]   Included: io.quarkus:quarkus-devtools-registry-client:jar:3.16.2
[DEBUG]   Included: io.quarkus:quarkus-devtools-base-codestarts:jar:3.16.2
[DEBUG]   Included: io.quarkus:quarkus-devtools-codestarts:jar:3.16.2
[DEBUG]   Included: io.quarkus.qute:qute-core:jar:3.16.2
[DEBUG]   Included: io.smallrye.reactive:mutiny:jar:2.6.2
[DEBUG]   Included: org.jctools:jctools-core:jar:4.0.5
[DEBUG]   Included: commons-io:commons-io:jar:2.17.0
[DEBUG]   Included: org.apache.commons:commons-compress:jar:1.27.1
[DEBUG]   Included: io.smallrye.common:smallrye-common-version:jar:2.7.0
[DEBUG]   Included: io.smallrye.common:smallrye-common-os:jar:2.7.0
[DEBUG]   Included: io.fabric8:maven-model-helper:jar:37
[DEBUG]   Included: org.jdom:jdom2:jar:2.0.6.1
[DEBUG]   Included: com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.18.1
[DEBUG]   Included: org.yaml:snakeyaml:jar:2.3
[DEBUG]   Included: com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.18.1
[DEBUG]   Included: org.apache.maven.resolver:maven-resolver-named-locks:jar:1.9.22
[DEBUG]   Included: org.codehaus.plexus:plexus-component-annotations:jar:2.1.0
[DEBUG]   Included: jakarta.enterprise:jakarta.enterprise.cdi-api:jar:4.1.0
[DEBUG]   Included: jakarta.enterprise:jakarta.enterprise.lang-model:jar:4.1.0
[DEBUG]   Included: jakarta.el:jakarta.el-api:jar:5.0.1
[DEBUG]   Included: jakarta.interceptor:jakarta.interceptor-api:jar:2.2.0
[DEBUG]   Included: org.codejive:java-properties:jar:0.0.7
[DEBUG]   Included: io.quarkus:quarkus-analytics-common:jar:3.16.2
[DEBUG]   Included: com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.18.1
[DEBUG]   Included: io.quarkus:quarkus-devtools-message-writer:jar:3.16.2
[DEBUG]   Included: io.quarkus:quarkus-cyclonedx-generator:jar:3.16.2
[DEBUG]   Included: org.cyclonedx:cyclonedx-core-java:jar:9.0.5
[DEBUG]   Included: org.apache.commons:commons-collections4:jar:4.4
[DEBUG]   Included: com.github.package-url:packageurl-java:jar:1.5.0
[DEBUG]   Included: com.fasterxml.jackson.dataformat:jackson-dataformat-xml:jar:2.18.1
[DEBUG]   Included: org.codehaus.woodstox:stax2-api:jar:4.2.2
[DEBUG]   Included: com.fasterxml.woodstox:woodstox-core:jar:7.0.0
[DEBUG]   Included: com.networknt:json-schema-validator:jar:1.5.1
[DEBUG]   Included: com.ethlo.time:itu:jar:1.10.2
[DEBUG]   Included: org.freemarker:freemarker:jar:2.3.33
[DEBUG]   Included: org.eclipse.parsson:parsson:jar:1.1.7
[DEBUG]   Included: jakarta.json:jakarta.json-api:jar:2.1.3
[DEBUG]   Included: com.fasterxml.jackson.core:jackson-databind:jar:2.18.1
[DEBUG]   Included: com.fasterxml.jackson.core:jackson-annotations:jar:2.18.1
[DEBUG]   Included: com.fasterxml.jackson.core:jackson-core:jar:2.18.1
[DEBUG]   Included: org.twdata.maven:mojo-executor:jar:2.4.0
[DEBUG]   Included: org.codehaus.plexus:plexus-utils:jar:3.5.1
[DEBUG]   Included: org.jboss.slf4j:slf4j-jboss-logmanager:jar:2.0.0.Final
[DEBUG] Loading mojo io.quarkus.platform:quarkus-maven-plugin:3.16.2:dev from plugin realm ClassRealm[plugin>io.quarkus.platform:quarkus-maven-plugin:3.16.2, parent: jdk.internal.loader.ClassLoaders$AppClassLoader@18ff02e4]
[DEBUG] Configuring mojo execution 'io.quarkus.platform:quarkus-maven-plugin:3.16.2:dev:default-cli' with basic configurator -->
[DEBUG]   (f) buildDir = /Users/<USER>/Desktop/Liden/Backend/quakers_mbs/target
[DEBUG]   (f) deleteDevJar = true
[DEBUG]   (f) modules = []
[DEBUG]   (f) mojoExecution = io.quarkus.platform:quarkus-maven-plugin:3.16.2:dev {execution: default-cli}
[DEBUG]   (f) outputDirectory = /Users/<USER>/Desktop/Liden/Backend/quakers_mbs/target/classes
[DEBUG]   (f) pluginRepos = [central (https://repo.maven.apache.org/maven2, default, releases)]
[DEBUG]   (f) project = MavenProject: com.mossplay.games:quakers_app:1.0.1 @ /Users/<USER>/Desktop/Liden/Backend/quakers_mbs/pom.xml
[DEBUG]   (f) release = 21
[DEBUG]   (f) repoSession = org.eclipse.aether.DefaultRepositorySystemSession@696174d3
[DEBUG]   (f) repos = [central (https://repo.maven.apache.org/maven2, default, releases)]
[DEBUG]   (f) session = org.apache.maven.execution.MavenSession@66715ca9
[DEBUG]   (f) skipPlugins = [org.codehaus.mojo:flatten-maven-plugin]
[DEBUG]   (f) sourceDir = /Users/<USER>/Desktop/Liden/Backend/quakers_mbs/src/main/java
[DEBUG] -- end configuration --
[DEBUG] Detected Maven Version: 3.9.9
[DEBUG] Detected Maven Version (3.9.9)  is allowed in [3.8.6,).
[DEBUG] Skipping default-test of org.apache.maven.plugins:maven-surefire-plugin:3.5.0
[DEBUG] Skipping default-clean of org.apache.maven.plugins:maven-clean-plugin:3.2.0
[DEBUG] Skipping default-jar of org.apache.maven.plugins:maven-jar-plugin:3.4.1
[DEBUG] Skipping default-install of org.apache.maven.plugins:maven-install-plugin:3.1.2
[DEBUG] Skipping default-deploy of org.apache.maven.plugins:maven-deploy-plugin:3.1.2
[DEBUG] Skipping default-site of org.apache.maven.plugins:maven-site-plugin:3.12.1
[DEBUG] Skipping default-deploy of org.apache.maven.plugins:maven-site-plugin:3.12.1
[INFO] Invoking resources:3.3.1:resources (default-resources) @ quakers_app
[DEBUG] Running executeMojo for Plugin [org.apache.maven.plugins:maven-resources-plugin]
[DEBUG] Attempting to load plugin Plugin [org.apache.maven.plugins:maven-resources-plugin] using pluginManager org.apache.maven.plugin.DefaultBuildPluginManager@563172d3 and repositories [central (https://repo.maven.apache.org/maven2, default, releases)]
[DEBUG] Dependency collection stats {ConflictMarker.analyzeTime=1003625, ConflictMarker.markTime=45959, ConflictMarker.nodeCount=13, ConflictIdSorter.graphTime=367583, ConflictIdSorter.topsortTime=133667, ConflictIdSorter.conflictIdCount=9, ConflictIdSorter.conflictIdCycleCount=0, ConflictResolver.totalTime=4123542, ConflictResolver.conflictItemCount=13, DfDependencyCollector.collectTime=94981666, DfDependencyCollector.transformTime=5767625}
[DEBUG] org.apache.maven.plugins:maven-resources-plugin:jar:3.3.1
[DEBUG]    org.codehaus.plexus:plexus-interpolation:jar:1.26:runtime
[DEBUG]    org.codehaus.plexus:plexus-utils:jar:3.5.1:compile
[DEBUG]    org.apache.maven.shared:maven-filtering:jar:3.3.1:compile
[DEBUG]       javax.inject:javax.inject:jar:1:compile
[DEBUG]       org.slf4j:slf4j-api:jar:1.7.36:compile
[DEBUG]       org.sonatype.plexus:plexus-build-api:jar:0.0.7:compile
[DEBUG]    commons-io:commons-io:jar:2.11.0:compile
[DEBUG]    org.apache.commons:commons-lang3:jar:3.12.0:compile
[DEBUG] Created new class realm plugin>org.apache.maven.plugins:maven-resources-plugin:3.3.1
[DEBUG] Importing foreign packages into class realm plugin>org.apache.maven.plugins:maven-resources-plugin:3.3.1
[DEBUG]   Imported:  < project>com.mossplay.games:quakers_app:1.0.1
[DEBUG] Populating class realm plugin>org.apache.maven.plugins:maven-resources-plugin:3.3.1
[DEBUG]   Included: org.apache.maven.plugins:maven-resources-plugin:jar:3.3.1
[DEBUG]   Included: org.codehaus.plexus:plexus-interpolation:jar:1.26
[DEBUG]   Included: org.codehaus.plexus:plexus-utils:jar:3.5.1
[DEBUG]   Included: org.apache.maven.shared:maven-filtering:jar:3.3.1
[DEBUG]   Included: org.sonatype.plexus:plexus-build-api:jar:0.0.7
[DEBUG]   Included: commons-io:commons-io:jar:2.11.0
[DEBUG]   Included: org.apache.commons:commons-lang3:jar:3.12.0
[DEBUG] Loading mojo org.apache.maven.plugins:maven-resources-plugin:3.3.1:resources from plugin realm ClassRealm[plugin>org.apache.maven.plugins:maven-resources-plugin:3.3.1, parent: jdk.internal.loader.ClassLoaders$AppClassLoader@18ff02e4]
[DEBUG] Configuring mojo execution 'org.apache.maven.plugins:maven-resources-plugin:3.3.1:resources:null' with basic configurator -->
[DEBUG]   (f) addDefaultExcludes = true
[DEBUG]   (f) buildFilters = []
[DEBUG]   (f) encoding = UTF-8
[DEBUG]   (f) escapeWindowsPaths = true
[DEBUG]   (f) fileNameFiltering = false
[DEBUG]   (s) includeEmptyDirs = false
[DEBUG]   (s) outputDirectory = /Users/<USER>/Desktop/Liden/Backend/quakers_mbs/target/classes
[DEBUG]   (s) overwrite = false
[DEBUG]   (f) project = MavenProject: com.mossplay.games:quakers_app:1.0.1 @ /Users/<USER>/Desktop/Liden/Backend/quakers_mbs/pom.xml
[DEBUG]   (s) resources = [Resource {targetPath: null, filtering: false, FileSet {directory: /Users/<USER>/Desktop/Liden/Backend/quakers_mbs/src/main/resources, PatternSet [includes: {}, excludes: {}]}}]
[DEBUG]   (f) session = org.apache.maven.execution.MavenSession@66715ca9
[DEBUG]   (f) skip = false
[DEBUG]   (f) supportMultiLineFiltering = false
[DEBUG]   (f) useBuildFilters = true
[DEBUG]   (s) useDefaultDelimiters = true
[DEBUG] -- end configuration --
[DEBUG] properties used:
[DEBUG] apple.awt.application.name: Launcher
[DEBUG] classworlds.conf: /usr/local/Cellar/maven/3.9.9/libexec/bin/m2.conf
[DEBUG] compiler-plugin.version: 3.13.0
[DEBUG] env.COMMAND_MODE: unix2003
[DEBUG] env.HOME: /Users/<USER>
[DEBUG] env.JAVA_HOME: /usr/local/opt/openjdk/libexec/openjdk.jdk/Contents/Home
[DEBUG] env.LC_CTYPE: UTF-8
[DEBUG] env.LOGNAME: josphatmuindi
[DEBUG] env.MAVEN_CMD_LINE_ARGS:  quarkus:dev -X
[DEBUG] env.MAVEN_PROJECTBASEDIR: /Users/<USER>/Desktop/Liden/Backend/quakers_mbs
[DEBUG] env.OLDPWD: /Users/<USER>/Desktop/Liden/Backend/quakers_mbs
[DEBUG] env.PATH: /Users/<USER>/.jbang/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Users/<USER>/.rvm/bin
[DEBUG] env.PWD: /Users/<USER>/Desktop/Liden/Backend/quakers_mbs
[DEBUG] env.SHELL: /bin/zsh
[DEBUG] env.SHLVL: 1
[DEBUG] env.SSH_AUTH_SOCK: /private/tmp/com.apple.launchd.vJYNkwaJLW/Listeners
[DEBUG] env.TERM: xterm-256color
[DEBUG] env.TERMINAL_EMULATOR: JetBrains-JediTerm
[DEBUG] env.TERM_SESSION_ID: cff1facc-3acd-4019-a451-4f802d8cd117
[DEBUG] env.TMPDIR: /var/folders/4_/35_3g6455wg4n4nc4n85qrmh0000gn/T/
[DEBUG] env.USER: josphatmuindi
[DEBUG] env.XPC_FLAGS: 0x0
[DEBUG] env.XPC_SERVICE_NAME: 0
[DEBUG] env.__CFBundleIdentifier: com.jetbrains.intellij.ce
[DEBUG] env.__CF_USER_TEXT_ENCODING: 0x1F5:0x0:0x0
[DEBUG] env._system_arch: arm64
[DEBUG] env._system_name: OSX
[DEBUG] env._system_type: Darwin
[DEBUG] env._system_version: 15.5
[DEBUG] env.rvm_bin_path: /Users/<USER>/.rvm/bin
[DEBUG] env.rvm_path: /Users/<USER>/.rvm
[DEBUG] env.rvm_prefix: /Users/<USER>
[DEBUG] env.rvm_version: 1.29.3 (latest)
[DEBUG] file.encoding: UTF-8
[DEBUG] file.separator: /
[DEBUG] ftp.nonProxyHosts: local|*.local|169.254/16|*.169.254/16
[DEBUG] http.nonProxyHosts: local|*.local|169.254/16|*.169.254/16
[DEBUG] java.class.path: /usr/local/Cellar/maven/3.9.9/libexec/boot/plexus-classworlds-2.8.0.jar
[DEBUG] java.class.version: 67.0
[DEBUG] java.home: /usr/local/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home
[DEBUG] java.io.tmpdir: /var/folders/4_/35_3g6455wg4n4nc4n85qrmh0000gn/T/
[DEBUG] java.library.path: /Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.
[DEBUG] java.runtime.name: OpenJDK Runtime Environment
[DEBUG] java.runtime.version: 23.0.2
[DEBUG] java.specification.name: Java Platform API Specification
[DEBUG] java.specification.vendor: Oracle Corporation
[DEBUG] java.specification.version: 23
[DEBUG] java.vendor: Homebrew
[DEBUG] java.vendor.url: https://github.com/Homebrew/homebrew-core/issues
[DEBUG] java.vendor.url.bug: https://github.com/Homebrew/homebrew-core/issues
[DEBUG] java.vendor.version: Homebrew
[DEBUG] java.version: 23.0.2
[DEBUG] java.version.date: 2025-01-21
[DEBUG] java.vm.compressedOopsMode: Zero based
[DEBUG] java.vm.info: mixed mode, sharing
[DEBUG] java.vm.name: OpenJDK 64-Bit Server VM
[DEBUG] java.vm.specification.name: Java Virtual Machine Specification
[DEBUG] java.vm.specification.vendor: Oracle Corporation
[DEBUG] java.vm.specification.version: 23
[DEBUG] java.vm.vendor: Homebrew
[DEBUG] java.vm.version: 23.0.2
[DEBUG] jdk.debug: release
[DEBUG] library.jansi.path: /usr/local/Cellar/maven/3.9.9/libexec/lib/jansi-native
[DEBUG] line.separator: 

[DEBUG] maven.build.timestamp: 2025-06-03T13:32:10Z
[DEBUG] maven.build.version: Apache Maven 3.9.9 (8e8579a9e76f7d015ee5ec7bfcdc97d260186937)
[DEBUG] maven.compiler.release: 21
[DEBUG] maven.conf: /usr/local/Cellar/maven/3.9.9/libexec/conf
[DEBUG] maven.home: /usr/local/Cellar/maven/3.9.9/libexec
[DEBUG] maven.multiModuleProjectDirectory: /Users/<USER>/Desktop/Liden/Backend/quakers_mbs
[DEBUG] maven.version: 3.9.9
[DEBUG] native.encoding: UTF-8
[DEBUG] os.arch: x86_64
[DEBUG] os.name: Mac OS X
[DEBUG] os.version: 15.5
[DEBUG] path.separator: :
[DEBUG] project.baseUri: file:/Users/<USER>/Desktop/Liden/Backend/quakers_mbs/
[DEBUG] project.build.sourceEncoding: UTF-8
[DEBUG] project.reporting.outputEncoding: UTF-8
[DEBUG] quarkus.platform.artifact-id: quarkus-bom
[DEBUG] quarkus.platform.group-id: io.quarkus.platform
[DEBUG] quarkus.platform.version: 3.16.2
[DEBUG] skipITs: true
[DEBUG] socksNonProxyHosts: local|*.local|169.254/16|*.169.254/16
[DEBUG] stderr.encoding: UTF-8
[DEBUG] stdout.encoding: UTF-8
[DEBUG] sun.arch.data.model: 64
[DEBUG] sun.boot.library.path: /usr/local/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib
[DEBUG] sun.cpu.endian: little
[DEBUG] sun.io.unicode.encoding: UnicodeBig
[DEBUG] sun.java.command: org.codehaus.plexus.classworlds.launcher.Launcher quarkus:dev -X
[DEBUG] sun.java.launcher: SUN_STANDARD
[DEBUG] sun.jnu.encoding: UTF-8
[DEBUG] sun.management.compiler: HotSpot 64-Bit Tiered Compilers
[DEBUG] surefire-plugin.version: 3.5.0
[DEBUG] user.country: KE
[DEBUG] user.dir: /Users/<USER>/Desktop/Liden/Backend/quakers_mbs
[DEBUG] user.home: /Users/<USER>
[DEBUG] user.language: en
[DEBUG] user.name: josphatmuindi
[DEBUG] Using 'UTF-8' encoding to copy filtered resources.
[DEBUG] Using 'UTF-8' encoding to copy filtered properties files.
[DEBUG] resource with targetPath null
directory /Users/<USER>/Desktop/Liden/Backend/quakers_mbs/src/main/resources
excludes []
includes []
[DEBUG] ignoreDelta true
[INFO] Copying 2 resources from src/main/resources to target/classes
[DEBUG] Copying file application-prod.properties
[DEBUG] file application-prod.properties has a filtered file extension
[DEBUG] Using 'UTF-8' encoding to copy filtered resource 'application-prod.properties'.
[DEBUG] copy /Users/<USER>/Desktop/Liden/Backend/quakers_mbs/src/main/resources/application-prod.properties to /Users/<USER>/Desktop/Liden/Backend/quakers_mbs/target/classes/application-prod.properties
[DEBUG] Copying file application.properties
[DEBUG] file application.properties has a filtered file extension
[DEBUG] Using 'UTF-8' encoding to copy filtered resource 'application.properties'.
[DEBUG] copy /Users/<USER>/Desktop/Liden/Backend/quakers_mbs/src/main/resources/application.properties to /Users/<USER>/Desktop/Liden/Backend/quakers_mbs/target/classes/application.properties
[DEBUG] no user filter components
[INFO] Invoking quarkus:3.16.2:generate-code (default) @ quakers_app
[DEBUG] Running executeMojo for Plugin [io.quarkus.platform:quarkus-maven-plugin]
[DEBUG] Attempting to load plugin Plugin [io.quarkus.platform:quarkus-maven-plugin] using pluginManager org.apache.maven.plugin.DefaultBuildPluginManager@563172d3 and repositories [central (https://repo.maven.apache.org/maven2, default, releases)]
[DEBUG] Loading mojo io.quarkus.platform:quarkus-maven-plugin:3.16.2:generate-code from plugin realm ClassRealm[plugin>io.quarkus.platform:quarkus-maven-plugin:3.16.2, parent: jdk.internal.loader.ClassLoaders$AppClassLoader@18ff02e4]
[DEBUG] Configuring mojo execution 'io.quarkus.platform:quarkus-maven-plugin:3.16.2:generate-code:null' with basic configurator -->
[DEBUG]   (f) bootstrapId = default
[DEBUG]   (f) mode = DEVELOPMENT
[DEBUG]   (f) closeBootstrappedApp = false
[DEBUG]   (f) buildDir = /Users/<USER>/Desktop/Liden/Backend/quakers_mbs/target
[DEBUG]   (f) finalName = quakers_app-1.0.1
[DEBUG]   (f) ignoredEntries = []
[DEBUG]   (f) mojoExecution = io.quarkus.platform:quarkus-maven-plugin:3.16.2:generate-code {execution: null}
[DEBUG]   (f) project = MavenProject: com.mossplay.games:quakers_app:1.0.1 @ /Users/<USER>/Desktop/Liden/Backend/quakers_mbs/pom.xml
[DEBUG]   (f) properties = {}
[DEBUG]   (f) repoSession = org.eclipse.aether.DefaultRepositorySystemSession@696174d3
[DEBUG]   (f) repos = [central (https://repo.maven.apache.org/maven2, default, releases)]
[DEBUG]   (f) session = org.apache.maven.execution.MavenSession@66715ca9
[DEBUG]   (f) skipSourceGeneration = false
[DEBUG] -- end configuration --
[DEBUG] Bootstrapping Quarkus application in mode DEVELOPMENT
[DEBUG] [org.jboss.logging] (main) Logging Provider: io.quarkus.maven.MojoLogger found via service loader
[DEBUG] Created adapter factory; available factories [noop, semaphore-local, rwlock-local, file-lock]; available name mappers [discriminating, static, gav, file-static, file-gav, file-hgav]
[DEBUG] Using manager EnhancedLocalRepositoryManager with priority 10.0 for /Users/<USER>/.m2/repository
[DEBUG] Creating adapter using nameMapper 'gav' and factory 'rwlock-local'
[DEBUG] Using mirror maven-default-http-blocker (http://0.0.0.0/) for apache.snapshots (http://repository.apache.org/snapshots).
[DEBUG] Dependency collection stats {ConflictMarker.analyzeTime=1448084, ConflictMarker.markTime=692583, ConflictMarker.nodeCount=865, ConflictIdSorter.graphTime=1161542, ConflictIdSorter.topsortTime=516416, ConflictIdSorter.conflictIdCount=177, ConflictIdSorter.conflictIdCycleCount=0, ConflictResolver.totalTime=42494125, ConflictResolver.conflictItemCount=445, DfDependencyCollector.collectTime=710987708, DfDependencyCollector.transformTime=46823167}
[DEBUG] [io.quarkus.bootstrap.model.ApplicationModelBuilder] (main) Extension io.quarkus:quarkus-core:jar:3.16.2 is excluding io.smallrye:smallrye-config
[DEBUG] [io.quarkus.bootstrap.model.ApplicationModelBuilder] (main) Extension io.quarkus:quarkus-core:jar:3.16.2 is excluding javax.enterprise:cdi-api
[DEBUG] [io.quarkus.bootstrap.model.ApplicationModelBuilder] (main) Extension io.quarkus:quarkus-core:jar:3.16.2 is excluding org.jboss.spec.javax.annotation:jboss-annotations-api_1.2_spec
[DEBUG] [io.quarkus.bootstrap.model.ApplicationModelBuilder] (main) Extension io.quarkus:quarkus-core:jar:3.16.2 is excluding org.jboss.spec.javax.annotation:jboss-annotations-api_1.3_spec
[DEBUG] [io.quarkus.bootstrap.model.ApplicationModelBuilder] (main) Extension io.quarkus:quarkus-core:jar:3.16.2 is excluding org.jboss.spec.javax.interceptor:jboss-interceptors-api_1.2_spec
[DEBUG] [io.quarkus.bootstrap.model.ApplicationModelBuilder] (main) Extension io.quarkus:quarkus-core:jar:3.16.2 is excluding org.glassfish:javax.el
[DEBUG] [io.quarkus.bootstrap.model.ApplicationModelBuilder] (main) Extension io.quarkus:quarkus-core:jar:3.16.2 is excluding javax.annotation:javax.annotation-api
[DEBUG] [io.quarkus.bootstrap.model.ApplicationModelBuilder] (main) Extension io.quarkus:quarkus-core:jar:3.16.2 is excluding com.sun.activation:jakarta.activation
[DEBUG] [io.quarkus.bootstrap.model.ApplicationModelBuilder] (main) Extension io.quarkus:quarkus-core:jar:3.16.2 is excluding com.sun.activation:javax.activation
[DEBUG] [io.quarkus.bootstrap.model.ApplicationModelBuilder] (main) Extension io.quarkus:quarkus-core:jar:3.16.2 is excluding org.glassfish:jakarta.el
[DEBUG] [io.quarkus.bootstrap.model.ApplicationModelBuilder] (main) Extension io.quarkus:quarkus-core:jar:3.16.2 is excluding org.jboss.spec.javax.ws.rs:jboss-jaxrs-api_2.1_spec
[DEBUG] [io.quarkus.bootstrap.model.ApplicationModelBuilder] (main) Extension io.quarkus:quarkus-core:jar:3.16.2 is excluding org.jboss.spec.javax.xml.bind:jboss-jaxb-api_2.3_spec
[DEBUG] [io.quarkus.bootstrap.model.ApplicationModelBuilder] (main) Extension io.quarkus:quarkus-core:jar:3.16.2 is excluding org.jboss.spec.javax.transaction:jboss-transaction-api_1.3_spec
[DEBUG] [io.quarkus.bootstrap.model.ApplicationModelBuilder] (main) Extension io.quarkus:quarkus-core:jar:3.16.2 is making io.quarkus:quarkus-junit4-mock a lesser priority artifact
[DEBUG] [io.quarkus.bootstrap.model.ApplicationModelBuilder] (main) Extension io.quarkus:quarkus-jsonp:jar:3.16.2 is excluding javax.json:javax.json-api
[DEBUG] [io.quarkus.bootstrap.model.ApplicationModelBuilder] (main) Extension io.quarkus:quarkus-jsonp:jar:3.16.2 is excluding org.glassfish:javax.json
[DEBUG] [io.quarkus.bootstrap.model.ApplicationModelBuilder] (main) Extension io.quarkus:quarkus-jsonp:jar:3.16.2 is excluding org.glassfish:jakarta.json
[DEBUG] [io.quarkus.bootstrap.model.ApplicationModelBuilder] (main) Extension io.quarkus:quarkus-jsonp:jar:3.16.2 is excluding org.eclipse.parsson:jakarta.json
[DEBUG] [io.quarkus.bootstrap.model.ApplicationModelBuilder] (main) Extension io.quarkus:quarkus-narayana-jta:jar:3.16.2 is excluding org.jboss.spec.javax.transaction:jboss-transaction-api_1.2_spec
[DEBUG] [io.quarkus.bootstrap.model.ApplicationModelBuilder] (main) Extension io.quarkus:quarkus-narayana-jta:jar:3.16.2 is excluding org.jboss.spec.javax.transaction:jboss-transaction-api_1.3_spec
[DEBUG] [io.quarkus.bootstrap.model.ApplicationModelBuilder] (main) Extension io.quarkus:quarkus-narayana-jta:jar:3.16.2 is excluding javax.transaction:jta
[DEBUG] [io.quarkus.bootstrap.model.ApplicationModelBuilder] (main) Extension io.quarkus:quarkus-narayana-jta:jar:3.16.2 is excluding javax.transaction:javax.transaction-api
[DEBUG] [io.quarkus.bootstrap.model.ApplicationModelBuilder] (main) Extension io.quarkus:quarkus-narayana-jta:jar:3.16.2 is excluding org.apache.geronimo.specs:geronimo-jta_1.0.1B_spec
[DEBUG] [io.quarkus.bootstrap.model.ApplicationModelBuilder] (main) Extension io.quarkus:quarkus-narayana-jta:jar:3.16.2 is excluding org.apache.geronimo.specs:geronimo-jta_1.1_spec
[DEBUG] [io.quarkus.bootstrap.model.ApplicationModelBuilder] (main) Extension io.quarkus:quarkus-narayana-jta:jar:3.16.2 is excluding org.apache.geronimo.specs:geronimo-jta_1.2_spec
[DEBUG] [io.quarkus.bootstrap.model.ApplicationModelBuilder] (main) Extension io.quarkus:quarkus-narayana-jta:jar:3.16.2 is excluding org.glassfish.main.transaction:javax.transaction
[DEBUG] [io.quarkus.bootstrap.resolver.maven.IncubatingApplicationModelResolver] (ForkJoinPool.commonPool-worker-5) Collecting dependencies of io.quarkus:quarkus-smallrye-health-deployment:jar:3.16.2
[DEBUG] [io.quarkus.bootstrap.resolver.maven.IncubatingApplicationModelResolver] (ForkJoinPool.commonPool-worker-1) Collecting dependencies of io.quarkus:quarkus-rest-jackson-deployment:jar:3.16.2
[DEBUG] [io.quarkus.bootstrap.resolver.maven.IncubatingApplicationModelResolver] (ForkJoinPool.commonPool-worker-8) Collecting dependencies of io.quarkus:quarkus-jdbc-mysql-deployment:jar:3.16.2
[DEBUG] [io.quarkus.bootstrap.resolver.maven.IncubatingApplicationModelResolver] (ForkJoinPool.commonPool-worker-7) Collecting dependencies of io.quarkus:quarkus-smallrye-fault-tolerance-deployment:jar:3.16.2
[DEBUG] [io.quarkus.bootstrap.resolver.maven.IncubatingApplicationModelResolver] (ForkJoinPool.commonPool-worker-6) Collecting dependencies of io.quarkus:quarkus-rest-deployment:jar:3.16.2
[DEBUG] [io.quarkus.bootstrap.resolver.maven.IncubatingApplicationModelResolver] (ForkJoinPool.commonPool-worker-3) Collecting dependencies of io.quarkus:quarkus-container-image-jib-deployment:jar:3.16.2
[DEBUG] [io.quarkus.bootstrap.resolver.maven.IncubatingApplicationModelResolver] (ForkJoinPool.commonPool-worker-2) Collecting dependencies of io.quarkus:quarkus-agroal-deployment:jar:3.16.2
[DEBUG] [io.quarkus.bootstrap.resolver.maven.IncubatingApplicationModelResolver] (ForkJoinPool.commonPool-worker-9) Collecting dependencies of io.quarkus:quarkus-arc-deployment:jar:3.16.2
[DEBUG] Dependency collection stats {ConflictMarker.analyzeTime=117584, ConflictMarker.markTime=68791, ConflictMarker.nodeCount=271, ConflictIdSorter.graphTime=67666, ConflictIdSorter.topsortTime=34292, ConflictIdSorter.conflictIdCount=95, ConflictIdSorter.conflictIdCycleCount=0, ConflictResolver.totalTime=55213667, ConflictResolver.conflictItemCount=212, DfDependencyCollector.collectTime=211094750, DfDependencyCollector.transformTime=55519042}
[DEBUG] Dependency collection stats {ConflictMarker.analyzeTime=255459, ConflictMarker.markTime=119041, ConflictMarker.nodeCount=161, ConflictIdSorter.graphTime=1609291, ConflictIdSorter.topsortTime=32292, ConflictIdSorter.conflictIdCount=78, ConflictIdSorter.conflictIdCycleCount=0, ConflictResolver.totalTime=56641459, ConflictResolver.conflictItemCount=152, DfDependencyCollector.collectTime=207748709, DfDependencyCollector.transformTime=58685416}
[DEBUG] Dependency collection stats {ConflictMarker.analyzeTime=208125, ConflictMarker.markTime=87917, ConflictMarker.nodeCount=344, ConflictIdSorter.graphTime=117833, ConflictIdSorter.topsortTime=52959, ConflictIdSorter.conflictIdCount=135, ConflictIdSorter.conflictIdCycleCount=0, ConflictResolver.totalTime=22835000, ConflictResolver.conflictItemCount=297, DfDependencyCollector.collectTime=291529333, DfDependencyCollector.transformTime=23373250}
[DEBUG] Dependency collection stats {ConflictMarker.analyzeTime=86292, ConflictMarker.markTime=63541, ConflictMarker.nodeCount=269, ConflictIdSorter.graphTime=73250, ConflictIdSorter.topsortTime=41084, ConflictIdSorter.conflictIdCount=118, ConflictIdSorter.conflictIdCycleCount=0, ConflictResolver.totalTime=14477125, ConflictResolver.conflictItemCount=235, DfDependencyCollector.collectTime=300337250, DfDependencyCollector.transformTime=14760292}
[DEBUG] Dependency collection stats {ConflictMarker.analyzeTime=2078417, ConflictMarker.markTime=136917, ConflictMarker.nodeCount=328, ConflictIdSorter.graphTime=192000, ConflictIdSorter.topsortTime=52541, ConflictIdSorter.conflictIdCount=128, ConflictIdSorter.conflictIdCycleCount=0, ConflictResolver.totalTime=23440042, ConflictResolver.conflictItemCount=281, DfDependencyCollector.collectTime=289243209, DfDependencyCollector.transformTime=25949625}
[DEBUG] Dependency collection stats {ConflictMarker.analyzeTime=275459, ConflictMarker.markTime=168041, ConflictMarker.nodeCount=823, ConflictIdSorter.graphTime=243334, ConflictIdSorter.topsortTime=74416, ConflictIdSorter.conflictIdCount=197, ConflictIdSorter.conflictIdCycleCount=0, ConflictResolver.totalTime=19972209, ConflictResolver.conflictItemCount=502, DfDependencyCollector.collectTime=315387916, DfDependencyCollector.transformTime=22218875}
[DEBUG] Dependency collection stats {ConflictMarker.analyzeTime=403625, ConflictMarker.markTime=173041, ConflictMarker.nodeCount=855, ConflictIdSorter.graphTime=250584, ConflictIdSorter.topsortTime=79041, ConflictIdSorter.conflictIdCount=206, ConflictIdSorter.conflictIdCycleCount=0, ConflictResolver.totalTime=18434625, ConflictResolver.conflictItemCount=534, DfDependencyCollector.collectTime=321305583, DfDependencyCollector.transformTime=19372375}
[DEBUG] Dependency collection stats {ConflictMarker.analyzeTime=242750, ConflictMarker.markTime=166291, ConflictMarker.nodeCount=837, ConflictIdSorter.graphTime=217375, ConflictIdSorter.topsortTime=67000, ConflictIdSorter.conflictIdCount=191, ConflictIdSorter.conflictIdCycleCount=0, ConflictResolver.totalTime=15811042, ConflictResolver.conflictItemCount=462, DfDependencyCollector.collectTime=331020459, DfDependencyCollector.transformTime=16530375}
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-arc / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus.arc:arc / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[jakarta.transaction:jakarta.transaction-api / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-core / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[jakarta.inject:jakarta.inject-api / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding parent first element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye.common:smallrye-common-os / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye.common:smallrye-common-os / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding parent first element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-development-mode-spi / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-development-mode-spi / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye.config:smallrye-config / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye.config:smallrye-config-core / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye.common:smallrye-common-classloader / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye.config:smallrye-config-common / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding parent first element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.jboss.logmanager:jboss-logmanager / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.jboss.logmanager:jboss-logmanager / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding parent first element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye.common:smallrye-common-constraint / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye.common:smallrye-common-constraint / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding parent first element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye.common:smallrye-common-cpu / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye.common:smallrye-common-cpu / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding parent first element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye.common:smallrye-common-expression / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye.common:smallrye-common-expression / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding parent first element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye.common:smallrye-common-net / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye.common:smallrye-common-net / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding parent first element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye.common:smallrye-common-ref / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye.common:smallrye-common-ref / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.jboss.logging:jboss-logging-annotations / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.jboss.threads:jboss-threads / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding parent first element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye.common:smallrye-common-function / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye.common:smallrye-common-function / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding parent first element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.jboss.slf4j:slf4j-jboss-logmanager / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.jboss.slf4j:slf4j-jboss-logmanager / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.wildfly.common:wildfly-common / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding parent first element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-bootstrap-runner / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-bootstrap-runner / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding parent first element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-classloader-commons / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-classloader-commons / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding parent first element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye.common:smallrye-common-io / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye.common:smallrye-common-io / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-fs-util / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.eclipse.microprofile.context-propagation:microprofile-context-propagation-api / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-rest / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-rest-common / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus.resteasy.reactive:resteasy-reactive-common / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus.resteasy.reactive:resteasy-reactive-common-types / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-vertx / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-netty / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.netty:netty-codec / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.netty:netty-codec-haproxy / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding parent first element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-vertx-latebound-mdc-provider / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-vertx-latebound-mdc-provider / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye:smallrye-fault-tolerance-vertx / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus.resteasy.reactive:resteasy-reactive-vertx / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.vertx:vertx-web / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.vertx:vertx-web-common / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.vertx:vertx-auth-common / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.vertx:vertx-bridge-common / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye.reactive:smallrye-mutiny-vertx-core / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye.reactive:smallrye-mutiny-vertx-runtime / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye.reactive:vertx-mutiny-generator / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.vertx:vertx-codegen / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus.resteasy.reactive:resteasy-reactive / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus.vertx.utils:quarkus-vertx-utils / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[jakarta.ws.rs:jakarta.ws.rs-api / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[jakarta.xml.bind:jakarta.xml.bind-api / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[jakarta.activation:jakarta.activation-api / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-vertx-http / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-security-runtime-spi / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-tls-registry / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye.common:smallrye-common-vertx-context / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus.security:quarkus-security / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye.reactive:smallrye-mutiny-vertx-web / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye.reactive:smallrye-mutiny-vertx-web-common / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye.reactive:smallrye-mutiny-vertx-auth-common / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye.reactive:smallrye-mutiny-vertx-bridge-common / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye.reactive:smallrye-mutiny-vertx-uri-template / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.vertx:vertx-uri-template / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.github.crac:org-crac / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[com.aayushatharva.brotli4j:brotli4j / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[com.aayushatharva.brotli4j:service / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[com.aayushatharva.brotli4j:native-osx-x86_64 / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-jsonp / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding parent first element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.eclipse.parsson:parsson / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.eclipse.parsson:parsson / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-virtual-threads / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.vertx:vertx-core / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.netty:netty-common / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.netty:netty-buffer / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.netty:netty-transport / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.netty:netty-handler / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.netty:netty-transport-native-unix-common / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.netty:netty-handler-proxy / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.netty:netty-codec-socks / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.netty:netty-codec-http / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.netty:netty-codec-http2 / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.netty:netty-resolver / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.netty:netty-resolver-dns / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.netty:netty-codec-dns / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[com.fasterxml.jackson.core:jackson-core / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-rest-jackson / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-rest-jackson-common / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus.resteasy.reactive:resteasy-reactive-jackson / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[com.fasterxml.jackson.core:jackson-databind / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[com.fasterxml.jackson.core:jackson-annotations / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-jackson / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[com.fasterxml.jackson.datatype:jackson-datatype-jsr310 / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[com.fasterxml.jackson.datatype:jackson-datatype-jdk8 / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[com.fasterxml.jackson.module:jackson-module-parameter-names / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-jdbc-mysql / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding parent first element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[com.mysql:mysql-connector-j / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[com.mysql:mysql-connector-j / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.codehaus.jettison:jettison / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[com.google.code.gson:gson / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[com.google.errorprone:error_prone_annotations / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[jakarta.json.bind:jakarta.json.bind-api / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.projectlombok:lombok / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[redis.clients:jedis / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding parent first element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.slf4j:slf4j-api / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.slf4j:slf4j-api / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.apache.commons:commons-pool2 / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.json:json / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.apache.httpcomponents:fluent-hc / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.apache.httpcomponents:httpclient / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.apache.httpcomponents:httpcore / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[commons-codec:commons-codec / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[commons-logging:commons-logging / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[joda-time:joda-time / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.apache.commons:commons-lang3 / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[net.sf.uadetector:uadetector-resources / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[net.sf.uadetector:uadetector-core / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[net.sf.qualitycheck:quality-check / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[com.google.code.findbugs:jsr305 / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[javax.annotation:jsr250-api / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye.reactive:smallrye-reactive-messaging-api / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye.common:smallrye-common-annotation / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[jakarta.enterprise:jakarta.enterprise.cdi-api / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[jakarta.enterprise:jakarta.enterprise.lang-model / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[jakarta.el:jakarta.el-api / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[jakarta.interceptor:jakarta.interceptor-api / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.opentelemetry:opentelemetry-api / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.opentelemetry:opentelemetry-context / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding parent first element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.jboss.logging:jboss-logging / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.jboss.logging:jboss-logging / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye.reactive:smallrye-reactive-converter-api / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.reactivestreams:reactive-streams / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye.reactive:mutiny / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.jctools:jctools-core / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye.reactive:mutiny-zero / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye.reactive:mutiny-zero-flow-adapters / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.eclipse.microprofile.health:microprofile-health-api / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-agroal / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-datasource / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-datasource-common / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-narayana-jta / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-transaction-annotations / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye:smallrye-context-propagation-jta / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye.reactive:smallrye-reactive-converter-mutiny / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.jboss.narayana.jta:narayana-jta / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[jakarta.resource:jakarta.resource-api / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.jboss.invocation:jboss-invocation / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.eclipse.microprofile.reactive-streams-operators:microprofile-reactive-streams-operators-api / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.jboss.narayana.jts:narayana-jts-integration / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.agroal:agroal-api / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.agroal:agroal-narayana / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.jboss:jboss-transaction-spi / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.agroal:agroal-pool / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-credentials / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[javax.inject:javax.inject / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-smallrye-fault-tolerance / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-mutiny / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye.reactive:mutiny-smallrye-context-propagation / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye:smallrye-fault-tolerance / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.eclipse.microprofile.fault-tolerance:microprofile-fault-tolerance-api / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye:smallrye-fault-tolerance-api / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye:smallrye-fault-tolerance-core / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye:smallrye-fault-tolerance-autoconfig-core / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.eclipse.microprofile.config:microprofile-config-api / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[jakarta.annotation:jakarta.annotation-api / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.jboss.logging:commons-logging-jboss-logging / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-smallrye-context-propagation / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye:smallrye-context-propagation / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye:smallrye-context-propagation-api / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye:smallrye-context-propagation-storage / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye:smallrye-fault-tolerance-context-propagation / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye:smallrye-fault-tolerance-mutiny / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[com.rabbitmq:amqp-client / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[jakarta.validation:jakarta.validation-api / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-container-image-jib / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-container-image / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-smallrye-health / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye:smallrye-health / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye:smallrye-health-api / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding parent first element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[jakarta.json:jakarta.json-api / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[jakarta.json:jakarta.json-api / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye:smallrye-health-provided-checks / runtime=true resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.fusesource.jansi:jansi / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.aesh:readline / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.aesh:aesh / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.ow2.asm:asm-analysis / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.ow2.asm:asm-util / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus.gizmo:gizmo / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye:jandex / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.ow2.asm:asm / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.ow2.asm:asm-tree / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.ow2.asm:asm-commons / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-hibernate-validator-spi / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding parent first element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-class-change-agent / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-class-change-agent / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding parent first element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-bootstrap-app-model / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-bootstrap-app-model / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding parent first element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-bootstrap-core / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-bootstrap-core / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-devtools-utilities / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.eclipse.sisu:org.eclipse.sisu.inject / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-builder / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding parent first element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.graalvm.sdk:word / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.graalvm.sdk:word / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding parent first element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.graalvm.sdk:nativeimage / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.graalvm.sdk:nativeimage / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding parent first element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.opentest4j:opentest4j / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.opentest4j:opentest4j / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding parent first element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.junit.platform:junit-platform-commons / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.junit.platform:junit-platform-commons / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding parent first element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.junit.platform:junit-platform-engine / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.junit.platform:junit-platform-engine / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding parent first element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.apiguardian:apiguardian-api / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.apiguardian:apiguardian-api / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding parent first element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.junit.platform:junit-platform-launcher / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.junit.platform:junit-platform-launcher / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding parent first element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.junit.jupiter:junit-jupiter-api / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.junit.jupiter:junit-jupiter-api / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding parent first element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.junit.jupiter:junit-jupiter-params / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.junit.jupiter:junit-jupiter-params / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding parent first element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.junit.jupiter:junit-jupiter-engine / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.junit.jupiter:junit-jupiter-engine / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.junit.jupiter:junit-jupiter / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-core-deployment / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-smallrye-context-propagation-spi / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-vertx-http-dev-ui-spi / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus.arc:arc-processor / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-arc-test-supplement / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-arc-deployment / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus.resteasy.reactive:resteasy-reactive-common-processor / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus.resteasy.reactive:resteasy-reactive-processor / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-netty-deployment / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-jackson-spi / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-vertx-deployment-spi / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-vertx-deployment / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-vertx-http-deployment-spi / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-tls-registry-deployment / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-kubernetes-spi / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.mvnpm.at.vaadin:router / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.mvnpm:path-to-regexp / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.mvnpm.at.mvnpm:vaadin-webcomponents / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.mvnpm.at.vaadin:vaadin-usage-statistics / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.mvnpm.at.vaadin:vaadin-development-mode-detector / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.mvnpm.at.polymer:polymer / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.mvnpm.at.webcomponents:shadycss / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.mvnpm.at.open-wc:dedupe-mixin / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.mvnpm:lit / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.mvnpm.at.lit:reactive-element / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.mvnpm:lit-element / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.mvnpm:lit-html / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.mvnpm.at.lit-labs:ssr-dom-shim / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.mvnpm.at.types:trusted-types / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.mvnpm:lit-element-state / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.mvnpm:tslib / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.mvnpm:zrender / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.mvnpm:echarts / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.mvnpm.at.mvnpm:codeblock / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.mvnpm.at.mvnpm:qomponent / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.mvnpm:markdown-it / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.mvnpm:argparse / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.mvnpm:entities / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.mvnpm:linkify-it / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.mvnpm:mdurl / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.mvnpm:punycode.js / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.mvnpm:uc.micro / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.mvnpm:es-module-shims / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-vertx-http-dev-ui-resources / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.mvnpm:importmap / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus.qute:qute-core / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.yaml:snakeyaml / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-vertx-http-deployment / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-rest-spi-deployment / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-rest-server-spi-deployment / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-jaxrs-spi-deployment / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-security-spi / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-jsonp-deployment / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-rest-common-deployment / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-virtual-threads-deployment / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-rest-deployment / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-jackson-deployment / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-rest-jackson-common-deployment / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-rest-jackson-deployment / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-agroal-spi / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-datasource-deployment-spi / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.testcontainers:database-commons / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.testcontainers:jdbc / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.testcontainers:mysql / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[com.github.docker-java:docker-java-api / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.jetbrains:annotations / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.rnorth.duct-tape:duct-tape / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[com.github.docker-java:docker-java-transport / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[net.java.dev.jna:jna / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[com.github.docker-java:docker-java-transport-zerodep / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.testcontainers:testcontainers / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding lesser priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-junit4-mock / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-devservices-common / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-devservices-mysql / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-jdbc-mysql-deployment / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-kubernetes-service-binding-spi / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-devservices-deployment / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-datasource-deployment / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-narayana-jta-deployment / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-credentials-deployment / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-smallrye-health-spi / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-agroal-deployment / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-smallrye-context-propagation-deployment / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-mutiny-deployment / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-smallrye-fault-tolerance-deployment / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-container-image-spi / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-container-image-util / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-container-image-deployment / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[com.google.cloud.tools:jib-build-plan / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[com.google.j2objc:j2objc-annotations / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.grpc:grpc-api / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.grpc:grpc-context / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.opencensus:opencensus-api / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.opencensus:opencensus-contrib-http-util / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[com.google.http-client:google-http-client / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[com.google.http-client:google-http-client-apache-v2 / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[com.google.auto.value:auto-value-annotations / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[com.google.auth:google-auth-library-credentials / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[com.google.http-client:google-http-client-gson / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[com.google.auth:google-auth-library-oauth2-http / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[commons-io:commons-io / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.apache.commons:commons-compress / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[com.google.guava:failureaccess / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[com.google.guava:guava / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[com.google.cloud.tools:jib-core / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-container-image-jib-deployment / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[org.eclipse.microprofile.openapi:microprofile-openapi-api / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[com.fasterxml.jackson.dataformat:jackson-dataformat-yaml / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye:smallrye-open-api-core / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-smallrye-openapi-spi / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.smallrye:smallrye-health-ui / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[io.quarkus:quarkus-smallrye-health-deployment / runtime=false resources=null] to QuarkusClassLoader Augmentation Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader.lifecycle] (main) Creating class loader QuarkusClassLoader:Augmentation Class Loader: PROD for quakers_app-1.0.1@5dba3fdc
java.lang.RuntimeException: Created to log a stacktrace
    at io.quarkus.bootstrap.classloading.QuarkusClassLoader.<init> (QuarkusClassLoader.java:200)
    at io.quarkus.bootstrap.classloading.QuarkusClassLoader$Builder.build (QuarkusClassLoader.java:902)
    at io.quarkus.bootstrap.app.CuratedApplication.getOrCreateAugmentClassLoader (CuratedApplication.java:226)
    at io.quarkus.bootstrap.app.CuratedApplication.createDeploymentClassLoader (CuratedApplication.java:338)
    at io.quarkus.maven.GenerateCodeMojo.generateCode (GenerateCodeMojo.java:80)
    at io.quarkus.maven.GenerateCodeMojo.doExecute (GenerateCodeMojo.java:54)
    at io.quarkus.maven.QuarkusBootstrapMojo.execute (QuarkusBootstrapMojo.java:171)
    at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
    at org.twdata.maven.mojoexecutor.MojoExecutor.executeMojo (MojoExecutor.java:120)
    at io.quarkus.maven.DevMojo.executeGoal (DevMojo.java:749)
    at io.quarkus.maven.DevMojo.handleAutoCompile (DevMojo.java:702)
    at io.quarkus.maven.DevMojo.execute (DevMojo.java:426)
    at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
    at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
    at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
    at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
    at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
    at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
    at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:105)
    at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:73)
    at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreadedBuilder.java:53)
    at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
    at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
    at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
    at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
    at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
    at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
    at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
    at jdk.internal.reflect.DirectMethodHandleAccessor.invoke (DirectMethodHandleAccessor.java:103)
    at java.lang.reflect.Method.invoke (Method.java:580)
    at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
    at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
    at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
    at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader] (main) Adding normal priority element io.quarkus.bootstrap.classloading.PathTreeClassPathElement[/Users/<USER>/Desktop/Liden/Backend/quakers_mbs/target/classes runtime=true resources=null] to QuarkusClassLoader Deployment Class Loader: PROD for quakers_app-1.0.1
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader.lifecycle] (main) Creating class loader QuarkusClassLoader:Deployment Class Loader: PROD for quakers_app-1.0.1@bea5941
java.lang.RuntimeException: Created to log a stacktrace
    at io.quarkus.bootstrap.classloading.QuarkusClassLoader.<init> (QuarkusClassLoader.java:200)
    at io.quarkus.bootstrap.classloading.QuarkusClassLoader$Builder.build (QuarkusClassLoader.java:902)
    at io.quarkus.bootstrap.app.CuratedApplication.createDeploymentClassLoader (CuratedApplication.java:368)
    at io.quarkus.maven.GenerateCodeMojo.generateCode (GenerateCodeMojo.java:80)
    at io.quarkus.maven.GenerateCodeMojo.doExecute (GenerateCodeMojo.java:54)
    at io.quarkus.maven.QuarkusBootstrapMojo.execute (QuarkusBootstrapMojo.java:171)
    at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
    at org.twdata.maven.mojoexecutor.MojoExecutor.executeMojo (MojoExecutor.java:120)
    at io.quarkus.maven.DevMojo.executeGoal (DevMojo.java:749)
    at io.quarkus.maven.DevMojo.handleAutoCompile (DevMojo.java:702)
    at io.quarkus.maven.DevMojo.execute (DevMojo.java:426)
    at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
    at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
    at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
    at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
    at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
    at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
    at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:105)
    at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:73)
    at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreadedBuilder.java:53)
    at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
    at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
    at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
    at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
    at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
    at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
    at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
    at jdk.internal.reflect.DirectMethodHandleAccessor.invoke (DirectMethodHandleAccessor.java:103)
    at java.lang.reflect.Method.invoke (Method.java:580)
    at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
    at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
    at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
    at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader.lifecycle] (main) Closing class loader QuarkusClassLoader:Deployment Class Loader: PROD for quakers_app-1.0.1@bea5941
java.lang.RuntimeException: Created to log a stacktrace
    at io.quarkus.bootstrap.classloading.QuarkusClassLoader.close (QuarkusClassLoader.java:680)
    at io.quarkus.maven.GenerateCodeMojo.generateCode (GenerateCodeMojo.java:98)
    at io.quarkus.maven.GenerateCodeMojo.doExecute (GenerateCodeMojo.java:54)
    at io.quarkus.maven.QuarkusBootstrapMojo.execute (QuarkusBootstrapMojo.java:171)
    at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
    at org.twdata.maven.mojoexecutor.MojoExecutor.executeMojo (MojoExecutor.java:120)
    at io.quarkus.maven.DevMojo.executeGoal (DevMojo.java:749)
    at io.quarkus.maven.DevMojo.handleAutoCompile (DevMojo.java:702)
    at io.quarkus.maven.DevMojo.execute (DevMojo.java:426)
    at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
    at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
    at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
    at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
    at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
    at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
    at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:105)
    at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:73)
    at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreadedBuilder.java:53)
    at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
    at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
    at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
    at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
    at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
    at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
    at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
    at jdk.internal.reflect.DirectMethodHandleAccessor.invoke (DirectMethodHandleAccessor.java:103)
    at java.lang.reflect.Method.invoke (Method.java:580)
    at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
    at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
    at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
    at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
[INFO] Invoking compiler:3.13.0:compile (default-compile) @ quakers_app
[DEBUG] Running executeMojo for Plugin [org.apache.maven.plugins:maven-compiler-plugin]
[DEBUG] Attempting to load plugin Plugin [org.apache.maven.plugins:maven-compiler-plugin] using pluginManager org.apache.maven.plugin.DefaultBuildPluginManager@563172d3 and repositories [central (https://repo.maven.apache.org/maven2, default, releases)]
[DEBUG] Dependency collection stats {ConflictMarker.analyzeTime=534500, ConflictMarker.markTime=20875, ConflictMarker.nodeCount=22, ConflictIdSorter.graphTime=13667, ConflictIdSorter.topsortTime=272542, ConflictIdSorter.conflictIdCount=14, ConflictIdSorter.conflictIdCycleCount=0, ConflictResolver.totalTime=5049042, ConflictResolver.conflictItemCount=22, DfDependencyCollector.collectTime=60330959, DfDependencyCollector.transformTime=5908500}
[DEBUG] org.apache.maven.plugins:maven-compiler-plugin:jar:3.13.0
[DEBUG]    org.apache.maven.shared:maven-shared-utils:jar:3.4.2:compile
[DEBUG]       org.slf4j:slf4j-api:jar:1.7.36:compile
[DEBUG]       commons-io:commons-io:jar:2.11.0:compile
[DEBUG]    org.apache.maven.shared:maven-shared-incremental:jar:1.1:compile
[DEBUG]    org.codehaus.plexus:plexus-java:jar:1.2.0:compile
[DEBUG]       org.ow2.asm:asm:jar:9.6:compile
[DEBUG]       com.thoughtworks.qdox:qdox:jar:2.0.3:compile
[DEBUG]    org.codehaus.plexus:plexus-compiler-api:jar:2.15.0:compile
[DEBUG]    org.codehaus.plexus:plexus-compiler-manager:jar:2.15.0:compile
[DEBUG]       javax.inject:javax.inject:jar:1:compile
[DEBUG]       org.codehaus.plexus:plexus-xml:jar:3.0.0:compile (version managed from default)
[DEBUG]    org.codehaus.plexus:plexus-compiler-javac:jar:2.15.0:runtime
[DEBUG]    org.codehaus.plexus:plexus-utils:jar:4.0.0:compile
[DEBUG] Created new class realm plugin>org.apache.maven.plugins:maven-compiler-plugin:3.13.0
[DEBUG] Importing foreign packages into class realm plugin>org.apache.maven.plugins:maven-compiler-plugin:3.13.0
[DEBUG]   Imported:  < project>com.mossplay.games:quakers_app:1.0.1
[DEBUG] Populating class realm plugin>org.apache.maven.plugins:maven-compiler-plugin:3.13.0
[DEBUG]   Included: org.apache.maven.plugins:maven-compiler-plugin:jar:3.13.0
[DEBUG]   Included: org.apache.maven.shared:maven-shared-utils:jar:3.4.2
[DEBUG]   Included: commons-io:commons-io:jar:2.11.0
[DEBUG]   Included: org.apache.maven.shared:maven-shared-incremental:jar:1.1
[DEBUG]   Included: org.codehaus.plexus:plexus-java:jar:1.2.0
[DEBUG]   Included: org.ow2.asm:asm:jar:9.6
[DEBUG]   Included: com.thoughtworks.qdox:qdox:jar:2.0.3
[DEBUG]   Included: org.codehaus.plexus:plexus-compiler-api:jar:2.15.0
[DEBUG]   Included: org.codehaus.plexus:plexus-compiler-manager:jar:2.15.0
[DEBUG]   Included: org.codehaus.plexus:plexus-xml:jar:3.0.0
[DEBUG]   Included: org.codehaus.plexus:plexus-compiler-javac:jar:2.15.0
[DEBUG]   Included: org.codehaus.plexus:plexus-utils:jar:4.0.0
[DEBUG] Loading mojo org.apache.maven.plugins:maven-compiler-plugin:3.13.0:compile from plugin realm ClassRealm[plugin>org.apache.maven.plugins:maven-compiler-plugin:3.13.0, parent: jdk.internal.loader.ClassLoaders$AppClassLoader@18ff02e4]
[DEBUG] Configuring mojo execution 'org.apache.maven.plugins:maven-compiler-plugin:3.13.0:compile:null' with basic configurator -->
[DEBUG]   (f) compilerArgs = [-parameters]
[DEBUG]   (f) annotationProcessorPathsUseDepMgmt = false
[DEBUG]   (f) basedir = /Users/<USER>/Desktop/Liden/Backend/quakers_mbs
[DEBUG]   (f) buildDirectory = /Users/<USER>/Desktop/Liden/Backend/quakers_mbs/target
[DEBUG]   (f) compilePath = [/Users/<USER>/Desktop/Liden/Backend/quakers_mbs/target/classes, /Users/<USER>/.m2/repository/io/quarkus/quarkus-arc/3.16.2/quarkus-arc-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/arc/arc/3.16.2/arc-3.16.2.jar, /Users/<USER>/.m2/repository/jakarta/transaction/jakarta.transaction-api/2.0.1/jakarta.transaction-api-2.0.1.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-core/3.16.2/quarkus-core-3.16.2.jar, /Users/<USER>/.m2/repository/jakarta/inject/jakarta.inject-api/2.0.1/jakarta.inject-api-2.0.1.jar, /Users/<USER>/.m2/repository/io/smallrye/common/smallrye-common-os/2.7.0/smallrye-common-os-2.7.0.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-ide-launcher/3.16.2/quarkus-ide-launcher-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-development-mode-spi/3.16.2/quarkus-development-mode-spi-3.16.2.jar, /Users/<USER>/.m2/repository/io/smallrye/config/smallrye-config/3.9.1/smallrye-config-3.9.1.jar, /Users/<USER>/.m2/repository/io/smallrye/config/smallrye-config-core/3.9.1/smallrye-config-core-3.9.1.jar, /Users/<USER>/.m2/repository/io/smallrye/common/smallrye-common-classloader/2.7.0/smallrye-common-classloader-2.7.0.jar, /Users/<USER>/.m2/repository/io/smallrye/config/smallrye-config-common/3.9.1/smallrye-config-common-3.9.1.jar, /Users/<USER>/.m2/repository/org/jboss/logmanager/jboss-logmanager/3.0.6.Final/jboss-logmanager-3.0.6.Final.jar, /Users/<USER>/.m2/repository/io/smallrye/common/smallrye-common-constraint/2.7.0/smallrye-common-constraint-2.7.0.jar, /Users/<USER>/.m2/repository/io/smallrye/common/smallrye-common-cpu/2.7.0/smallrye-common-cpu-2.7.0.jar, /Users/<USER>/.m2/repository/io/smallrye/common/smallrye-common-expression/2.7.0/smallrye-common-expression-2.7.0.jar, /Users/<USER>/.m2/repository/io/smallrye/common/smallrye-common-net/2.7.0/smallrye-common-net-2.7.0.jar, /Users/<USER>/.m2/repository/io/smallrye/common/smallrye-common-ref/2.7.0/smallrye-common-ref-2.7.0.jar, /Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging-annotations/3.0.2.Final/jboss-logging-annotations-3.0.2.Final.jar, /Users/<USER>/.m2/repository/org/jboss/threads/jboss-threads/3.8.0.Final/jboss-threads-3.8.0.Final.jar, /Users/<USER>/.m2/repository/io/smallrye/common/smallrye-common-function/2.7.0/smallrye-common-function-2.7.0.jar, /Users/<USER>/.m2/repository/org/jboss/slf4j/slf4j-jboss-logmanager/2.0.0.Final/slf4j-jboss-logmanager-2.0.0.Final.jar, /Users/<USER>/.m2/repository/org/wildfly/common/wildfly-common/1.7.0.Final/wildfly-common-1.7.0.Final.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-bootstrap-runner/3.16.2/quarkus-bootstrap-runner-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-classloader-commons/3.16.2/quarkus-classloader-commons-3.16.2.jar, /Users/<USER>/.m2/repository/io/smallrye/common/smallrye-common-io/2.7.0/smallrye-common-io-2.7.0.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-fs-util/0.0.10/quarkus-fs-util-0.0.10.jar, /Users/<USER>/.m2/repository/org/eclipse/microprofile/context-propagation/microprofile-context-propagation-api/1.3/microprofile-context-propagation-api-1.3.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-rest/3.16.2/quarkus-rest-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-rest-common/3.16.2/quarkus-rest-common-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/resteasy/reactive/resteasy-reactive-common/3.16.2/resteasy-reactive-common-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/resteasy/reactive/resteasy-reactive-common-types/3.16.2/resteasy-reactive-common-types-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-vertx/3.16.2/quarkus-vertx-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-netty/3.16.2/quarkus-netty-3.16.2.jar, /Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.111.Final/netty-codec-4.1.111.Final.jar, /Users/<USER>/.m2/repository/io/netty/netty-codec-haproxy/4.1.111.Final/netty-codec-haproxy-4.1.111.Final.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-vertx-latebound-mdc-provider/3.16.2/quarkus-vertx-latebound-mdc-provider-3.16.2.jar, /Users/<USER>/.m2/repository/io/smallrye/smallrye-fault-tolerance-vertx/6.5.0/smallrye-fault-tolerance-vertx-6.5.0.jar, /Users/<USER>/.m2/repository/io/quarkus/resteasy/reactive/resteasy-reactive-vertx/3.16.2/resteasy-reactive-vertx-3.16.2.jar, /Users/<USER>/.m2/repository/io/vertx/vertx-web/4.5.10/vertx-web-4.5.10.jar, /Users/<USER>/.m2/repository/io/vertx/vertx-web-common/4.5.10/vertx-web-common-4.5.10.jar, /Users/<USER>/.m2/repository/io/vertx/vertx-auth-common/4.5.10/vertx-auth-common-4.5.10.jar, /Users/<USER>/.m2/repository/io/vertx/vertx-bridge-common/4.5.10/vertx-bridge-common-4.5.10.jar, /Users/<USER>/.m2/repository/io/smallrye/reactive/smallrye-mutiny-vertx-core/3.15.0/smallrye-mutiny-vertx-core-3.15.0.jar, /Users/<USER>/.m2/repository/io/smallrye/reactive/smallrye-mutiny-vertx-runtime/3.15.0/smallrye-mutiny-vertx-runtime-3.15.0.jar, /Users/<USER>/.m2/repository/io/smallrye/reactive/vertx-mutiny-generator/3.15.0/vertx-mutiny-generator-3.15.0.jar, /Users/<USER>/.m2/repository/io/vertx/vertx-codegen/4.5.10/vertx-codegen-4.5.10.jar, /Users/<USER>/.m2/repository/io/quarkus/resteasy/reactive/resteasy-reactive/3.16.2/resteasy-reactive-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/vertx/utils/quarkus-vertx-utils/3.16.2/quarkus-vertx-utils-3.16.2.jar, /Users/<USER>/.m2/repository/jakarta/ws/rs/jakarta.ws.rs-api/3.1.0/jakarta.ws.rs-api-3.1.0.jar, /Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar, /Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-vertx-http/3.16.2/quarkus-vertx-http-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-security-runtime-spi/3.16.2/quarkus-security-runtime-spi-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-tls-registry/3.16.2/quarkus-tls-registry-3.16.2.jar, /Users/<USER>/.m2/repository/io/smallrye/common/smallrye-common-vertx-context/2.7.0/smallrye-common-vertx-context-2.7.0.jar, /Users/<USER>/.m2/repository/io/quarkus/security/quarkus-security/2.1.0/quarkus-security-2.1.0.jar, /Users/<USER>/.m2/repository/io/smallrye/reactive/smallrye-mutiny-vertx-web/3.15.0/smallrye-mutiny-vertx-web-3.15.0.jar, /Users/<USER>/.m2/repository/io/smallrye/reactive/smallrye-mutiny-vertx-web-common/3.15.0/smallrye-mutiny-vertx-web-common-3.15.0.jar, /Users/<USER>/.m2/repository/io/smallrye/reactive/smallrye-mutiny-vertx-auth-common/3.15.0/smallrye-mutiny-vertx-auth-common-3.15.0.jar, /Users/<USER>/.m2/repository/io/smallrye/reactive/smallrye-mutiny-vertx-bridge-common/3.15.0/smallrye-mutiny-vertx-bridge-common-3.15.0.jar, /Users/<USER>/.m2/repository/io/smallrye/reactive/smallrye-mutiny-vertx-uri-template/3.15.0/smallrye-mutiny-vertx-uri-template-3.15.0.jar, /Users/<USER>/.m2/repository/io/vertx/vertx-uri-template/4.5.10/vertx-uri-template-4.5.10.jar, /Users/<USER>/.m2/repository/io/github/crac/org-crac/0.1.3/org-crac-0.1.3.jar, /Users/<USER>/.m2/repository/com/aayushatharva/brotli4j/brotli4j/1.16.0/brotli4j-1.16.0.jar, /Users/<USER>/.m2/repository/com/aayushatharva/brotli4j/service/1.16.0/service-1.16.0.jar, /Users/<USER>/.m2/repository/com/aayushatharva/brotli4j/native-osx-x86_64/1.16.0/native-osx-x86_64-1.16.0.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-jsonp/3.16.2/quarkus-jsonp-3.16.2.jar, /Users/<USER>/.m2/repository/org/eclipse/parsson/parsson/1.1.7/parsson-1.1.7.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-virtual-threads/3.16.2/quarkus-virtual-threads-3.16.2.jar, /Users/<USER>/.m2/repository/io/vertx/vertx-core/4.5.10/vertx-core-4.5.10.jar, /Users/<USER>/.m2/repository/io/netty/netty-common/4.1.111.Final/netty-common-4.1.111.Final.jar, /Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.111.Final/netty-buffer-4.1.111.Final.jar, /Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.111.Final/netty-transport-4.1.111.Final.jar, /Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.111.Final/netty-handler-4.1.111.Final.jar, /Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.111.Final/netty-transport-native-unix-common-4.1.111.Final.jar, /Users/<USER>/.m2/repository/io/netty/netty-handler-proxy/4.1.111.Final/netty-handler-proxy-4.1.111.Final.jar, /Users/<USER>/.m2/repository/io/netty/netty-codec-socks/4.1.111.Final/netty-codec-socks-4.1.111.Final.jar, /Users/<USER>/.m2/repository/io/netty/netty-codec-http/4.1.111.Final/netty-codec-http-4.1.111.Final.jar, /Users/<USER>/.m2/repository/io/netty/netty-codec-http2/4.1.111.Final/netty-codec-http2-4.1.111.Final.jar, /Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.111.Final/netty-resolver-4.1.111.Final.jar, /Users/<USER>/.m2/repository/io/netty/netty-resolver-dns/4.1.111.Final/netty-resolver-dns-4.1.111.Final.jar, /Users/<USER>/.m2/repository/io/netty/netty-codec-dns/4.1.111.Final/netty-codec-dns-4.1.111.Final.jar, /Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.18.1/jackson-core-2.18.1.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-rest-jackson/3.16.2/quarkus-rest-jackson-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-rest-jackson-common/3.16.2/quarkus-rest-jackson-common-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/resteasy/reactive/resteasy-reactive-jackson/3.16.2/resteasy-reactive-jackson-3.16.2.jar, /Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.18.1/jackson-databind-2.18.1.jar, /Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.18.1/jackson-annotations-2.18.1.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-jackson/3.16.2/quarkus-jackson-3.16.2.jar, /Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.18.1/jackson-datatype-jsr310-2.18.1.jar, /Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.18.1/jackson-datatype-jdk8-2.18.1.jar, /Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.18.1/jackson-module-parameter-names-2.18.1.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-jdbc-mysql/3.16.2/quarkus-jdbc-mysql-3.16.2.jar, /Users/<USER>/.m2/repository/com/mysql/mysql-connector-j/8.3.0/mysql-connector-j-8.3.0.jar, /Users/<USER>/.m2/repository/org/codehaus/jettison/jettison/1.5.4/jettison-1.5.4.jar, /Users/<USER>/.m2/repository/com/google/code/gson/gson/2.13.1/gson-2.13.1.jar, /Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.33.0/error_prone_annotations-2.33.0.jar, /Users/<USER>/.m2/repository/jakarta/json/bind/jakarta.json.bind-api/3.0.1/jakarta.json.bind-api-3.0.1.jar, /Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.24/lombok-1.18.24.jar, /Users/<USER>/.m2/repository/redis/clients/jedis/5.1.5/jedis-5.1.5.jar, /Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.6/slf4j-api-2.0.6.jar, /Users/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.12.0/commons-pool2-2.12.0.jar, /Users/<USER>/.m2/repository/org/json/json/20231013/json-20231013.jar, /Users/<USER>/.m2/repository/org/apache/httpcomponents/fluent-hc/4.5.14/fluent-hc-4.5.14.jar, /Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.14/httpclient-4.5.14.jar, /Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar, /Users/<USER>/.m2/repository/commons-codec/commons-codec/1.17.1/commons-codec-1.17.1.jar, /Users/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar, /Users/<USER>/.m2/repository/joda-time/joda-time/2.8.1/joda-time-2.8.1.jar, /Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.17.0/commons-lang3-3.17.0.jar, /Users/<USER>/.m2/repository/net/sf/uadetector/uadetector-resources/2014.10/uadetector-resources-2014.10.jar, /Users/<USER>/.m2/repository/net/sf/uadetector/uadetector-core/0.9.22/uadetector-core-0.9.22.jar, /Users/<USER>/.m2/repository/net/sf/qualitycheck/quality-check/1.3/quality-check-1.3.jar, /Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar, /Users/<USER>/.m2/repository/javax/annotation/jsr250-api/1.0/jsr250-api-1.0.jar, /Users/<USER>/.m2/repository/io/smallrye/reactive/smallrye-reactive-messaging-api/4.25.0/smallrye-reactive-messaging-api-4.25.0.jar, /Users/<USER>/.m2/repository/io/smallrye/common/smallrye-common-annotation/2.7.0/smallrye-common-annotation-2.7.0.jar, /Users/<USER>/.m2/repository/jakarta/enterprise/jakarta.enterprise.cdi-api/4.1.0/jakarta.enterprise.cdi-api-4.1.0.jar, /Users/<USER>/.m2/repository/jakarta/enterprise/jakarta.enterprise.lang-model/4.1.0/jakarta.enterprise.lang-model-4.1.0.jar, /Users/<USER>/.m2/repository/jakarta/el/jakarta.el-api/5.0.1/jakarta.el-api-5.0.1.jar, /Users/<USER>/.m2/repository/jakarta/interceptor/jakarta.interceptor-api/2.2.0/jakarta.interceptor-api-2.2.0.jar, /Users/<USER>/.m2/repository/io/opentelemetry/opentelemetry-api/1.42.1/opentelemetry-api-1.42.1.jar, /Users/<USER>/.m2/repository/io/opentelemetry/opentelemetry-context/1.42.1/opentelemetry-context-1.42.1.jar, /Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.6.1.Final/jboss-logging-3.6.1.Final.jar, /Users/<USER>/.m2/repository/io/smallrye/reactive/smallrye-reactive-converter-api/3.0.1/smallrye-reactive-converter-api-3.0.1.jar, /Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar, /Users/<USER>/.m2/repository/io/smallrye/reactive/mutiny/2.6.2/mutiny-2.6.2.jar, /Users/<USER>/.m2/repository/org/jctools/jctools-core/4.0.5/jctools-core-4.0.5.jar, /Users/<USER>/.m2/repository/io/smallrye/reactive/mutiny-zero/1.1.0/mutiny-zero-1.1.0.jar, /Users/<USER>/.m2/repository/io/smallrye/reactive/mutiny-zero-flow-adapters/1.1.0/mutiny-zero-flow-adapters-1.1.0.jar, /Users/<USER>/.m2/repository/org/eclipse/microprofile/health/microprofile-health-api/4.0.1/microprofile-health-api-4.0.1.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-agroal/3.16.2/quarkus-agroal-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-datasource/3.16.2/quarkus-datasource-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-datasource-common/3.16.2/quarkus-datasource-common-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-narayana-jta/3.16.2/quarkus-narayana-jta-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-transaction-annotations/3.16.2/quarkus-transaction-annotations-3.16.2.jar, /Users/<USER>/.m2/repository/io/smallrye/smallrye-context-propagation-jta/2.1.2/smallrye-context-propagation-jta-2.1.2.jar, /Users/<USER>/.m2/repository/io/smallrye/reactive/smallrye-reactive-converter-mutiny/3.0.1/smallrye-reactive-converter-mutiny-3.0.1.jar, /Users/<USER>/.m2/repository/org/jboss/narayana/jta/narayana-jta/7.0.2.Final/narayana-jta-7.0.2.Final.jar, /Users/<USER>/.m2/repository/jakarta/resource/jakarta.resource-api/2.1.0/jakarta.resource-api-2.1.0.jar, /Users/<USER>/.m2/repository/org/jboss/invocation/jboss-invocation/2.0.0.Final/jboss-invocation-2.0.0.Final.jar, /Users/<USER>/.m2/repository/org/eclipse/microprofile/reactive-streams-operators/microprofile-reactive-streams-operators-api/3.0.1/microprofile-reactive-streams-operators-api-3.0.1.jar, /Users/<USER>/.m2/repository/org/jboss/narayana/jts/narayana-jts-integration/7.0.2.Final/narayana-jts-integration-7.0.2.Final.jar, /Users/<USER>/.m2/repository/io/agroal/agroal-api/2.5/agroal-api-2.5.jar, /Users/<USER>/.m2/repository/io/agroal/agroal-narayana/2.5/agroal-narayana-2.5.jar, /Users/<USER>/.m2/repository/org/jboss/jboss-transaction-spi/8.0.0.Final/jboss-transaction-spi-8.0.0.Final.jar, /Users/<USER>/.m2/repository/io/agroal/agroal-pool/2.5/agroal-pool-2.5.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-credentials/3.16.2/quarkus-credentials-3.16.2.jar, /Users/<USER>/.m2/repository/javax/inject/javax.inject/1/javax.inject-1.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-smallrye-fault-tolerance/3.16.2/quarkus-smallrye-fault-tolerance-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-mutiny/3.16.2/quarkus-mutiny-3.16.2.jar, /Users/<USER>/.m2/repository/io/smallrye/reactive/mutiny-smallrye-context-propagation/2.6.2/mutiny-smallrye-context-propagation-2.6.2.jar, /Users/<USER>/.m2/repository/io/smallrye/smallrye-fault-tolerance/6.5.0/smallrye-fault-tolerance-6.5.0.jar, /Users/<USER>/.m2/repository/org/eclipse/microprofile/fault-tolerance/microprofile-fault-tolerance-api/4.1.1/microprofile-fault-tolerance-api-4.1.1.jar, /Users/<USER>/.m2/repository/io/smallrye/smallrye-fault-tolerance-api/6.5.0/smallrye-fault-tolerance-api-6.5.0.jar, /Users/<USER>/.m2/repository/io/smallrye/smallrye-fault-tolerance-core/6.5.0/smallrye-fault-tolerance-core-6.5.0.jar, /Users/<USER>/.m2/repository/io/smallrye/smallrye-fault-tolerance-autoconfig-core/6.5.0/smallrye-fault-tolerance-autoconfig-core-6.5.0.jar, /Users/<USER>/.m2/repository/org/eclipse/microprofile/config/microprofile-config-api/3.1/microprofile-config-api-3.1.jar, /Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/3.0.0/jakarta.annotation-api-3.0.0.jar, /Users/<USER>/.m2/repository/org/jboss/logging/commons-logging-jboss-logging/1.0.0.Final/commons-logging-jboss-logging-1.0.0.Final.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-smallrye-context-propagation/3.16.2/quarkus-smallrye-context-propagation-3.16.2.jar, /Users/<USER>/.m2/repository/io/smallrye/smallrye-context-propagation/2.1.2/smallrye-context-propagation-2.1.2.jar, /Users/<USER>/.m2/repository/io/smallrye/smallrye-context-propagation-api/2.1.2/smallrye-context-propagation-api-2.1.2.jar, /Users/<USER>/.m2/repository/io/smallrye/smallrye-context-propagation-storage/2.1.2/smallrye-context-propagation-storage-2.1.2.jar, /Users/<USER>/.m2/repository/io/smallrye/smallrye-fault-tolerance-context-propagation/6.5.0/smallrye-fault-tolerance-context-propagation-6.5.0.jar, /Users/<USER>/.m2/repository/io/smallrye/smallrye-fault-tolerance-mutiny/6.5.0/smallrye-fault-tolerance-mutiny-6.5.0.jar, /Users/<USER>/.m2/repository/com/rabbitmq/amqp-client/5.25.0/amqp-client-5.25.0.jar, /Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.1.1/jakarta.validation-api-3.1.1.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-container-image-jib/3.16.2/quarkus-container-image-jib-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-container-image/3.16.2/quarkus-container-image-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-smallrye-health/3.16.2/quarkus-smallrye-health-3.16.2.jar, /Users/<USER>/.m2/repository/io/smallrye/smallrye-health/4.1.0/smallrye-health-4.1.0.jar, /Users/<USER>/.m2/repository/io/smallrye/smallrye-health-api/4.1.0/smallrye-health-api-4.1.0.jar, /Users/<USER>/.m2/repository/jakarta/json/jakarta.json-api/2.1.3/jakarta.json-api-2.1.3.jar, /Users/<USER>/.m2/repository/io/smallrye/smallrye-health-provided-checks/4.1.0/smallrye-health-provided-checks-4.1.0.jar]
[DEBUG]   (f) compileSourceRoots = [/Users/<USER>/Desktop/Liden/Backend/quakers_mbs/src/main/java]
[DEBUG]   (f) compilerId = javac
[DEBUG]   (f) createMissingPackageInfoClass = true
[DEBUG]   (f) debug = true
[DEBUG]   (f) debugFileName = javac
[DEBUG]   (f) enablePreview = false
[DEBUG]   (f) encoding = UTF-8
[DEBUG]   (f) failOnError = true
[DEBUG]   (f) failOnWarning = false
[DEBUG]   (f) fileExtensions = [jar, class]
[DEBUG]   (f) forceJavacCompilerUse = false
[DEBUG]   (f) forceLegacyJavacApi = false
[DEBUG]   (f) fork = false
[DEBUG]   (f) generatedSourcesDirectory = /Users/<USER>/Desktop/Liden/Backend/quakers_mbs/target/generated-sources/annotations
[DEBUG]   (f) mojoExecution = org.apache.maven.plugins:maven-compiler-plugin:3.13.0:compile {execution: null}
[DEBUG]   (f) optimize = false
[DEBUG]   (f) outputDirectory = /Users/<USER>/Desktop/Liden/Backend/quakers_mbs/target/classes
[DEBUG]   (f) parameters = false
[DEBUG]   (f) project = MavenProject: com.mossplay.games:quakers_app:1.0.1 @ /Users/<USER>/Desktop/Liden/Backend/quakers_mbs/pom.xml
[DEBUG]   (f) projectArtifact = com.mossplay.games:quakers_app:jar:1.0.1
[DEBUG]   (s) release = 21
[DEBUG]   (f) session = org.apache.maven.execution.MavenSession@66715ca9
[DEBUG]   (f) showCompilationChanges = false
[DEBUG]   (f) showDeprecation = false
[DEBUG]   (f) showWarnings = true
[DEBUG]   (f) skipMultiThreadWarning = false
[DEBUG]   (f) source = 1.8
[DEBUG]   (f) staleMillis = 0
[DEBUG]   (s) target = 1.8
[DEBUG]   (f) useIncrementalCompilation = true
[DEBUG]   (f) verbose = false
[DEBUG] -- end configuration --
[DEBUG] Using compiler 'javac'.
[DEBUG] Adding /Users/<USER>/Desktop/Liden/Backend/quakers_mbs/target/generated-sources/annotations to compile source roots:
  /Users/<USER>/Desktop/Liden/Backend/quakers_mbs/src/main/java
[DEBUG] New compile source roots:
  /Users/<USER>/Desktop/Liden/Backend/quakers_mbs/src/main/java
  /Users/<USER>/Desktop/Liden/Backend/quakers_mbs/target/generated-sources/annotations
[DEBUG] CompilerReuseStrategy: reuseCreated
[DEBUG] useIncrementalCompilation enabled
[INFO] Nothing to compile - all classes are up to date.
[INFO] Invoking resources:3.3.1:testResources (default-testResources) @ quakers_app
[DEBUG] Running executeMojo for Plugin [org.apache.maven.plugins:maven-resources-plugin]
[DEBUG] Attempting to load plugin Plugin [org.apache.maven.plugins:maven-resources-plugin] using pluginManager org.apache.maven.plugin.DefaultBuildPluginManager@563172d3 and repositories [central (https://repo.maven.apache.org/maven2, default, releases)]
[DEBUG] Loading mojo org.apache.maven.plugins:maven-resources-plugin:3.3.1:testResources from plugin realm ClassRealm[plugin>org.apache.maven.plugins:maven-resources-plugin:3.3.1, parent: jdk.internal.loader.ClassLoaders$AppClassLoader@18ff02e4]
[DEBUG] Configuring mojo execution 'org.apache.maven.plugins:maven-resources-plugin:3.3.1:testResources:null' with basic configurator -->
[DEBUG]   (f) addDefaultExcludes = true
[DEBUG]   (f) buildFilters = []
[DEBUG]   (f) encoding = UTF-8
[DEBUG]   (f) escapeWindowsPaths = true
[DEBUG]   (f) fileNameFiltering = false
[DEBUG]   (s) includeEmptyDirs = false
[DEBUG]   (s) outputDirectory = /Users/<USER>/Desktop/Liden/Backend/quakers_mbs/target/test-classes
[DEBUG]   (s) overwrite = false
[DEBUG]   (f) project = MavenProject: com.mossplay.games:quakers_app:1.0.1 @ /Users/<USER>/Desktop/Liden/Backend/quakers_mbs/pom.xml
[DEBUG]   (s) resources = [Resource {targetPath: null, filtering: false, FileSet {directory: /Users/<USER>/Desktop/Liden/Backend/quakers_mbs/src/test/resources, PatternSet [includes: {}, excludes: {}]}}]
[DEBUG]   (f) session = org.apache.maven.execution.MavenSession@66715ca9
[DEBUG]   (f) skip = false
[DEBUG]   (f) supportMultiLineFiltering = false
[DEBUG]   (f) useBuildFilters = true
[DEBUG]   (s) useDefaultDelimiters = true
[DEBUG] -- end configuration --
[DEBUG] properties used:
[DEBUG] apple.awt.application.name: Launcher
[DEBUG] classworlds.conf: /usr/local/Cellar/maven/3.9.9/libexec/bin/m2.conf
[DEBUG] compiler-plugin.version: 3.13.0
[DEBUG] env.COMMAND_MODE: unix2003
[DEBUG] env.HOME: /Users/<USER>
[DEBUG] env.JAVA_HOME: /usr/local/opt/openjdk/libexec/openjdk.jdk/Contents/Home
[DEBUG] env.LC_CTYPE: UTF-8
[DEBUG] env.LOGNAME: josphatmuindi
[DEBUG] env.MAVEN_CMD_LINE_ARGS:  quarkus:dev -X
[DEBUG] env.MAVEN_PROJECTBASEDIR: /Users/<USER>/Desktop/Liden/Backend/quakers_mbs
[DEBUG] env.OLDPWD: /Users/<USER>/Desktop/Liden/Backend/quakers_mbs
[DEBUG] env.PATH: /Users/<USER>/.jbang/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Users/<USER>/.rvm/bin
[DEBUG] env.PWD: /Users/<USER>/Desktop/Liden/Backend/quakers_mbs
[DEBUG] env.SHELL: /bin/zsh
[DEBUG] env.SHLVL: 1
[DEBUG] env.SSH_AUTH_SOCK: /private/tmp/com.apple.launchd.vJYNkwaJLW/Listeners
[DEBUG] env.TERM: xterm-256color
[DEBUG] env.TERMINAL_EMULATOR: JetBrains-JediTerm
[DEBUG] env.TERM_SESSION_ID: cff1facc-3acd-4019-a451-4f802d8cd117
[DEBUG] env.TMPDIR: /var/folders/4_/35_3g6455wg4n4nc4n85qrmh0000gn/T/
[DEBUG] env.USER: josphatmuindi
[DEBUG] env.XPC_FLAGS: 0x0
[DEBUG] env.XPC_SERVICE_NAME: 0
[DEBUG] env.__CFBundleIdentifier: com.jetbrains.intellij.ce
[DEBUG] env.__CF_USER_TEXT_ENCODING: 0x1F5:0x0:0x0
[DEBUG] env._system_arch: arm64
[DEBUG] env._system_name: OSX
[DEBUG] env._system_type: Darwin
[DEBUG] env._system_version: 15.5
[DEBUG] env.rvm_bin_path: /Users/<USER>/.rvm/bin
[DEBUG] env.rvm_path: /Users/<USER>/.rvm
[DEBUG] env.rvm_prefix: /Users/<USER>
[DEBUG] env.rvm_version: 1.29.3 (latest)
[DEBUG] file.encoding: UTF-8
[DEBUG] file.separator: /
[DEBUG] ftp.nonProxyHosts: local|*.local|169.254/16|*.169.254/16
[DEBUG] http.nonProxyHosts: local|*.local|169.254/16|*.169.254/16
[DEBUG] java.class.path: /usr/local/Cellar/maven/3.9.9/libexec/boot/plexus-classworlds-2.8.0.jar
[DEBUG] java.class.version: 67.0
[DEBUG] java.home: /usr/local/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home
[DEBUG] java.io.tmpdir: /var/folders/4_/35_3g6455wg4n4nc4n85qrmh0000gn/T/
[DEBUG] java.library.path: /Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.
[DEBUG] java.runtime.name: OpenJDK Runtime Environment
[DEBUG] java.runtime.version: 23.0.2
[DEBUG] java.specification.name: Java Platform API Specification
[DEBUG] java.specification.vendor: Oracle Corporation
[DEBUG] java.specification.version: 23
[DEBUG] java.vendor: Homebrew
[DEBUG] java.vendor.url: https://github.com/Homebrew/homebrew-core/issues
[DEBUG] java.vendor.url.bug: https://github.com/Homebrew/homebrew-core/issues
[DEBUG] java.vendor.version: Homebrew
[DEBUG] java.version: 23.0.2
[DEBUG] java.version.date: 2025-01-21
[DEBUG] java.vm.compressedOopsMode: Zero based
[DEBUG] java.vm.info: mixed mode, sharing
[DEBUG] java.vm.name: OpenJDK 64-Bit Server VM
[DEBUG] java.vm.specification.name: Java Virtual Machine Specification
[DEBUG] java.vm.specification.vendor: Oracle Corporation
[DEBUG] java.vm.specification.version: 23
[DEBUG] java.vm.vendor: Homebrew
[DEBUG] java.vm.version: 23.0.2
[DEBUG] jdk.debug: release
[DEBUG] library.jansi.path: /usr/local/Cellar/maven/3.9.9/libexec/lib/jansi-native
[DEBUG] line.separator: 

[DEBUG] maven.build.timestamp: 2025-06-03T13:32:13Z
[DEBUG] maven.build.version: Apache Maven 3.9.9 (8e8579a9e76f7d015ee5ec7bfcdc97d260186937)
[DEBUG] maven.compiler.release: 21
[DEBUG] maven.conf: /usr/local/Cellar/maven/3.9.9/libexec/conf
[DEBUG] maven.home: /usr/local/Cellar/maven/3.9.9/libexec
[DEBUG] maven.multiModuleProjectDirectory: /Users/<USER>/Desktop/Liden/Backend/quakers_mbs
[DEBUG] maven.version: 3.9.9
[DEBUG] native.encoding: UTF-8
[DEBUG] os.arch: x86_64
[DEBUG] os.name: Mac OS X
[DEBUG] os.version: 15.5
[DEBUG] path.separator: :
[DEBUG] project.baseUri: file:/Users/<USER>/Desktop/Liden/Backend/quakers_mbs/
[DEBUG] project.build.sourceEncoding: UTF-8
[DEBUG] project.reporting.outputEncoding: UTF-8
[DEBUG] quarkus.platform.artifact-id: quarkus-bom
[DEBUG] quarkus.platform.group-id: io.quarkus.platform
[DEBUG] quarkus.platform.version: 3.16.2
[DEBUG] skipITs: true
[DEBUG] socksNonProxyHosts: local|*.local|169.254/16|*.169.254/16
[DEBUG] stderr.encoding: UTF-8
[DEBUG] stdout.encoding: UTF-8
[DEBUG] sun.arch.data.model: 64
[DEBUG] sun.boot.library.path: /usr/local/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib
[DEBUG] sun.cpu.endian: little
[DEBUG] sun.io.unicode.encoding: UnicodeBig
[DEBUG] sun.java.command: org.codehaus.plexus.classworlds.launcher.Launcher quarkus:dev -X
[DEBUG] sun.java.launcher: SUN_STANDARD
[DEBUG] sun.jnu.encoding: UTF-8
[DEBUG] sun.management.compiler: HotSpot 64-Bit Tiered Compilers
[DEBUG] surefire-plugin.version: 3.5.0
[DEBUG] user.country: KE
[DEBUG] user.dir: /Users/<USER>/Desktop/Liden/Backend/quakers_mbs
[DEBUG] user.home: /Users/<USER>
[DEBUG] user.language: en
[DEBUG] user.name: josphatmuindi
[DEBUG] Using 'UTF-8' encoding to copy filtered resources.
[DEBUG] Using 'UTF-8' encoding to copy filtered properties files.
[DEBUG] resource with targetPath null
directory /Users/<USER>/Desktop/Liden/Backend/quakers_mbs/src/test/resources
excludes []
includes []
[INFO] skip non existing resourceDirectory /Users/<USER>/Desktop/Liden/Backend/quakers_mbs/src/test/resources
[DEBUG] no user filter components
[INFO] Invoking compiler:3.13.0:testCompile (default-testCompile) @ quakers_app
[DEBUG] Running executeMojo for Plugin [org.apache.maven.plugins:maven-compiler-plugin]
[DEBUG] Attempting to load plugin Plugin [org.apache.maven.plugins:maven-compiler-plugin] using pluginManager org.apache.maven.plugin.DefaultBuildPluginManager@563172d3 and repositories [central (https://repo.maven.apache.org/maven2, default, releases)]
[DEBUG] Loading mojo org.apache.maven.plugins:maven-compiler-plugin:3.13.0:testCompile from plugin realm ClassRealm[plugin>org.apache.maven.plugins:maven-compiler-plugin:3.13.0, parent: jdk.internal.loader.ClassLoaders$AppClassLoader@18ff02e4]
[DEBUG] Configuring mojo execution 'org.apache.maven.plugins:maven-compiler-plugin:3.13.0:testCompile:null' with basic configurator -->
[DEBUG]   (f) compilerArgs = [-parameters]
[DEBUG]   (f) annotationProcessorPathsUseDepMgmt = false
[DEBUG]   (f) basedir = /Users/<USER>/Desktop/Liden/Backend/quakers_mbs
[DEBUG]   (f) buildDirectory = /Users/<USER>/Desktop/Liden/Backend/quakers_mbs/target
[DEBUG]   (f) compileSourceRoots = [/Users/<USER>/Desktop/Liden/Backend/quakers_mbs/src/test/java]
[DEBUG]   (f) compilerId = javac
[DEBUG]   (f) createMissingPackageInfoClass = true
[DEBUG]   (f) debug = true
[DEBUG]   (f) debugFileName = javac-test
[DEBUG]   (f) enablePreview = false
[DEBUG]   (f) encoding = UTF-8
[DEBUG]   (f) failOnError = true
[DEBUG]   (f) failOnWarning = false
[DEBUG]   (f) fileExtensions = [jar, class]
[DEBUG]   (f) forceJavacCompilerUse = false
[DEBUG]   (f) forceLegacyJavacApi = false
[DEBUG]   (f) fork = false
[DEBUG]   (f) generatedTestSourcesDirectory = /Users/<USER>/Desktop/Liden/Backend/quakers_mbs/target/generated-test-sources/test-annotations
[DEBUG]   (f) mojoExecution = org.apache.maven.plugins:maven-compiler-plugin:3.13.0:testCompile {execution: null}
[DEBUG]   (f) optimize = false
[DEBUG]   (f) outputDirectory = /Users/<USER>/Desktop/Liden/Backend/quakers_mbs/target/test-classes
[DEBUG]   (f) parameters = false
[DEBUG]   (f) project = MavenProject: com.mossplay.games:quakers_app:1.0.1 @ /Users/<USER>/Desktop/Liden/Backend/quakers_mbs/pom.xml
[DEBUG]   (s) release = 21
[DEBUG]   (f) session = org.apache.maven.execution.MavenSession@66715ca9
[DEBUG]   (f) showCompilationChanges = false
[DEBUG]   (f) showDeprecation = false
[DEBUG]   (f) showWarnings = true
[DEBUG]   (f) skipMultiThreadWarning = false
[DEBUG]   (f) source = 1.8
[DEBUG]   (f) staleMillis = 0
[DEBUG]   (s) target = 1.8
[DEBUG]   (f) testPath = [/Users/<USER>/Desktop/Liden/Backend/quakers_mbs/target/test-classes, /Users/<USER>/Desktop/Liden/Backend/quakers_mbs/target/classes, /Users/<USER>/.m2/repository/io/quarkus/quarkus-arc/3.16.2/quarkus-arc-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/arc/arc/3.16.2/arc-3.16.2.jar, /Users/<USER>/.m2/repository/jakarta/transaction/jakarta.transaction-api/2.0.1/jakarta.transaction-api-2.0.1.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-core/3.16.2/quarkus-core-3.16.2.jar, /Users/<USER>/.m2/repository/jakarta/inject/jakarta.inject-api/2.0.1/jakarta.inject-api-2.0.1.jar, /Users/<USER>/.m2/repository/io/smallrye/common/smallrye-common-os/2.7.0/smallrye-common-os-2.7.0.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-ide-launcher/3.16.2/quarkus-ide-launcher-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-development-mode-spi/3.16.2/quarkus-development-mode-spi-3.16.2.jar, /Users/<USER>/.m2/repository/io/smallrye/config/smallrye-config/3.9.1/smallrye-config-3.9.1.jar, /Users/<USER>/.m2/repository/io/smallrye/config/smallrye-config-core/3.9.1/smallrye-config-core-3.9.1.jar, /Users/<USER>/.m2/repository/io/smallrye/common/smallrye-common-classloader/2.7.0/smallrye-common-classloader-2.7.0.jar, /Users/<USER>/.m2/repository/io/smallrye/config/smallrye-config-common/3.9.1/smallrye-config-common-3.9.1.jar, /Users/<USER>/.m2/repository/org/jboss/logmanager/jboss-logmanager/3.0.6.Final/jboss-logmanager-3.0.6.Final.jar, /Users/<USER>/.m2/repository/io/smallrye/common/smallrye-common-constraint/2.7.0/smallrye-common-constraint-2.7.0.jar, /Users/<USER>/.m2/repository/io/smallrye/common/smallrye-common-cpu/2.7.0/smallrye-common-cpu-2.7.0.jar, /Users/<USER>/.m2/repository/io/smallrye/common/smallrye-common-expression/2.7.0/smallrye-common-expression-2.7.0.jar, /Users/<USER>/.m2/repository/io/smallrye/common/smallrye-common-net/2.7.0/smallrye-common-net-2.7.0.jar, /Users/<USER>/.m2/repository/io/smallrye/common/smallrye-common-ref/2.7.0/smallrye-common-ref-2.7.0.jar, /Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging-annotations/3.0.2.Final/jboss-logging-annotations-3.0.2.Final.jar, /Users/<USER>/.m2/repository/org/jboss/threads/jboss-threads/3.8.0.Final/jboss-threads-3.8.0.Final.jar, /Users/<USER>/.m2/repository/io/smallrye/common/smallrye-common-function/2.7.0/smallrye-common-function-2.7.0.jar, /Users/<USER>/.m2/repository/org/jboss/slf4j/slf4j-jboss-logmanager/2.0.0.Final/slf4j-jboss-logmanager-2.0.0.Final.jar, /Users/<USER>/.m2/repository/org/wildfly/common/wildfly-common/1.7.0.Final/wildfly-common-1.7.0.Final.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-bootstrap-runner/3.16.2/quarkus-bootstrap-runner-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-classloader-commons/3.16.2/quarkus-classloader-commons-3.16.2.jar, /Users/<USER>/.m2/repository/io/smallrye/common/smallrye-common-io/2.7.0/smallrye-common-io-2.7.0.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-fs-util/0.0.10/quarkus-fs-util-0.0.10.jar, /Users/<USER>/.m2/repository/org/eclipse/microprofile/context-propagation/microprofile-context-propagation-api/1.3/microprofile-context-propagation-api-1.3.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-rest/3.16.2/quarkus-rest-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-rest-common/3.16.2/quarkus-rest-common-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/resteasy/reactive/resteasy-reactive-common/3.16.2/resteasy-reactive-common-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/resteasy/reactive/resteasy-reactive-common-types/3.16.2/resteasy-reactive-common-types-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-vertx/3.16.2/quarkus-vertx-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-netty/3.16.2/quarkus-netty-3.16.2.jar, /Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.111.Final/netty-codec-4.1.111.Final.jar, /Users/<USER>/.m2/repository/io/netty/netty-codec-haproxy/4.1.111.Final/netty-codec-haproxy-4.1.111.Final.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-vertx-latebound-mdc-provider/3.16.2/quarkus-vertx-latebound-mdc-provider-3.16.2.jar, /Users/<USER>/.m2/repository/io/smallrye/smallrye-fault-tolerance-vertx/6.5.0/smallrye-fault-tolerance-vertx-6.5.0.jar, /Users/<USER>/.m2/repository/io/quarkus/resteasy/reactive/resteasy-reactive-vertx/3.16.2/resteasy-reactive-vertx-3.16.2.jar, /Users/<USER>/.m2/repository/io/vertx/vertx-web/4.5.10/vertx-web-4.5.10.jar, /Users/<USER>/.m2/repository/io/vertx/vertx-web-common/4.5.10/vertx-web-common-4.5.10.jar, /Users/<USER>/.m2/repository/io/vertx/vertx-auth-common/4.5.10/vertx-auth-common-4.5.10.jar, /Users/<USER>/.m2/repository/io/vertx/vertx-bridge-common/4.5.10/vertx-bridge-common-4.5.10.jar, /Users/<USER>/.m2/repository/io/smallrye/reactive/smallrye-mutiny-vertx-core/3.15.0/smallrye-mutiny-vertx-core-3.15.0.jar, /Users/<USER>/.m2/repository/io/smallrye/reactive/smallrye-mutiny-vertx-runtime/3.15.0/smallrye-mutiny-vertx-runtime-3.15.0.jar, /Users/<USER>/.m2/repository/io/smallrye/reactive/vertx-mutiny-generator/3.15.0/vertx-mutiny-generator-3.15.0.jar, /Users/<USER>/.m2/repository/io/vertx/vertx-codegen/4.5.10/vertx-codegen-4.5.10.jar, /Users/<USER>/.m2/repository/io/quarkus/resteasy/reactive/resteasy-reactive/3.16.2/resteasy-reactive-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/vertx/utils/quarkus-vertx-utils/3.16.2/quarkus-vertx-utils-3.16.2.jar, /Users/<USER>/.m2/repository/jakarta/ws/rs/jakarta.ws.rs-api/3.1.0/jakarta.ws.rs-api-3.1.0.jar, /Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.2/jakarta.xml.bind-api-4.0.2.jar, /Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.3/jakarta.activation-api-2.1.3.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-vertx-http/3.16.2/quarkus-vertx-http-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-security-runtime-spi/3.16.2/quarkus-security-runtime-spi-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-tls-registry/3.16.2/quarkus-tls-registry-3.16.2.jar, /Users/<USER>/.m2/repository/io/smallrye/common/smallrye-common-vertx-context/2.7.0/smallrye-common-vertx-context-2.7.0.jar, /Users/<USER>/.m2/repository/io/quarkus/security/quarkus-security/2.1.0/quarkus-security-2.1.0.jar, /Users/<USER>/.m2/repository/io/smallrye/reactive/smallrye-mutiny-vertx-web/3.15.0/smallrye-mutiny-vertx-web-3.15.0.jar, /Users/<USER>/.m2/repository/io/smallrye/reactive/smallrye-mutiny-vertx-web-common/3.15.0/smallrye-mutiny-vertx-web-common-3.15.0.jar, /Users/<USER>/.m2/repository/io/smallrye/reactive/smallrye-mutiny-vertx-auth-common/3.15.0/smallrye-mutiny-vertx-auth-common-3.15.0.jar, /Users/<USER>/.m2/repository/io/smallrye/reactive/smallrye-mutiny-vertx-bridge-common/3.15.0/smallrye-mutiny-vertx-bridge-common-3.15.0.jar, /Users/<USER>/.m2/repository/io/smallrye/reactive/smallrye-mutiny-vertx-uri-template/3.15.0/smallrye-mutiny-vertx-uri-template-3.15.0.jar, /Users/<USER>/.m2/repository/io/vertx/vertx-uri-template/4.5.10/vertx-uri-template-4.5.10.jar, /Users/<USER>/.m2/repository/io/github/crac/org-crac/0.1.3/org-crac-0.1.3.jar, /Users/<USER>/.m2/repository/com/aayushatharva/brotli4j/brotli4j/1.16.0/brotli4j-1.16.0.jar, /Users/<USER>/.m2/repository/com/aayushatharva/brotli4j/service/1.16.0/service-1.16.0.jar, /Users/<USER>/.m2/repository/com/aayushatharva/brotli4j/native-osx-x86_64/1.16.0/native-osx-x86_64-1.16.0.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-jsonp/3.16.2/quarkus-jsonp-3.16.2.jar, /Users/<USER>/.m2/repository/org/eclipse/parsson/parsson/1.1.7/parsson-1.1.7.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-virtual-threads/3.16.2/quarkus-virtual-threads-3.16.2.jar, /Users/<USER>/.m2/repository/io/vertx/vertx-core/4.5.10/vertx-core-4.5.10.jar, /Users/<USER>/.m2/repository/io/netty/netty-common/4.1.111.Final/netty-common-4.1.111.Final.jar, /Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.111.Final/netty-buffer-4.1.111.Final.jar, /Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.111.Final/netty-transport-4.1.111.Final.jar, /Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.111.Final/netty-handler-4.1.111.Final.jar, /Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.111.Final/netty-transport-native-unix-common-4.1.111.Final.jar, /Users/<USER>/.m2/repository/io/netty/netty-handler-proxy/4.1.111.Final/netty-handler-proxy-4.1.111.Final.jar, /Users/<USER>/.m2/repository/io/netty/netty-codec-socks/4.1.111.Final/netty-codec-socks-4.1.111.Final.jar, /Users/<USER>/.m2/repository/io/netty/netty-codec-http/4.1.111.Final/netty-codec-http-4.1.111.Final.jar, /Users/<USER>/.m2/repository/io/netty/netty-codec-http2/4.1.111.Final/netty-codec-http2-4.1.111.Final.jar, /Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.111.Final/netty-resolver-4.1.111.Final.jar, /Users/<USER>/.m2/repository/io/netty/netty-resolver-dns/4.1.111.Final/netty-resolver-dns-4.1.111.Final.jar, /Users/<USER>/.m2/repository/io/netty/netty-codec-dns/4.1.111.Final/netty-codec-dns-4.1.111.Final.jar, /Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.18.1/jackson-core-2.18.1.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-rest-jackson/3.16.2/quarkus-rest-jackson-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-rest-jackson-common/3.16.2/quarkus-rest-jackson-common-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/resteasy/reactive/resteasy-reactive-jackson/3.16.2/resteasy-reactive-jackson-3.16.2.jar, /Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.18.1/jackson-databind-2.18.1.jar, /Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.18.1/jackson-annotations-2.18.1.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-jackson/3.16.2/quarkus-jackson-3.16.2.jar, /Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.18.1/jackson-datatype-jsr310-2.18.1.jar, /Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.18.1/jackson-datatype-jdk8-2.18.1.jar, /Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.18.1/jackson-module-parameter-names-2.18.1.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-jdbc-mysql/3.16.2/quarkus-jdbc-mysql-3.16.2.jar, /Users/<USER>/.m2/repository/com/mysql/mysql-connector-j/8.3.0/mysql-connector-j-8.3.0.jar, /Users/<USER>/.m2/repository/org/codehaus/jettison/jettison/1.5.4/jettison-1.5.4.jar, /Users/<USER>/.m2/repository/com/google/code/gson/gson/2.13.1/gson-2.13.1.jar, /Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.33.0/error_prone_annotations-2.33.0.jar, /Users/<USER>/.m2/repository/jakarta/json/bind/jakarta.json.bind-api/3.0.1/jakarta.json.bind-api-3.0.1.jar, /Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.24/lombok-1.18.24.jar, /Users/<USER>/.m2/repository/redis/clients/jedis/5.1.5/jedis-5.1.5.jar, /Users/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.6/slf4j-api-2.0.6.jar, /Users/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.12.0/commons-pool2-2.12.0.jar, /Users/<USER>/.m2/repository/org/json/json/20231013/json-20231013.jar, /Users/<USER>/.m2/repository/org/apache/httpcomponents/fluent-hc/4.5.14/fluent-hc-4.5.14.jar, /Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.14/httpclient-4.5.14.jar, /Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar, /Users/<USER>/.m2/repository/commons-codec/commons-codec/1.17.1/commons-codec-1.17.1.jar, /Users/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar, /Users/<USER>/.m2/repository/joda-time/joda-time/2.8.1/joda-time-2.8.1.jar, /Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.17.0/commons-lang3-3.17.0.jar, /Users/<USER>/.m2/repository/net/sf/uadetector/uadetector-resources/2014.10/uadetector-resources-2014.10.jar, /Users/<USER>/.m2/repository/net/sf/uadetector/uadetector-core/0.9.22/uadetector-core-0.9.22.jar, /Users/<USER>/.m2/repository/net/sf/qualitycheck/quality-check/1.3/quality-check-1.3.jar, /Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar, /Users/<USER>/.m2/repository/javax/annotation/jsr250-api/1.0/jsr250-api-1.0.jar, /Users/<USER>/.m2/repository/io/smallrye/reactive/smallrye-reactive-messaging-api/4.25.0/smallrye-reactive-messaging-api-4.25.0.jar, /Users/<USER>/.m2/repository/io/smallrye/common/smallrye-common-annotation/2.7.0/smallrye-common-annotation-2.7.0.jar, /Users/<USER>/.m2/repository/jakarta/enterprise/jakarta.enterprise.cdi-api/4.1.0/jakarta.enterprise.cdi-api-4.1.0.jar, /Users/<USER>/.m2/repository/jakarta/enterprise/jakarta.enterprise.lang-model/4.1.0/jakarta.enterprise.lang-model-4.1.0.jar, /Users/<USER>/.m2/repository/jakarta/el/jakarta.el-api/5.0.1/jakarta.el-api-5.0.1.jar, /Users/<USER>/.m2/repository/jakarta/interceptor/jakarta.interceptor-api/2.2.0/jakarta.interceptor-api-2.2.0.jar, /Users/<USER>/.m2/repository/io/opentelemetry/opentelemetry-api/1.42.1/opentelemetry-api-1.42.1.jar, /Users/<USER>/.m2/repository/io/opentelemetry/opentelemetry-context/1.42.1/opentelemetry-context-1.42.1.jar, /Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.6.1.Final/jboss-logging-3.6.1.Final.jar, /Users/<USER>/.m2/repository/io/smallrye/reactive/smallrye-reactive-converter-api/3.0.1/smallrye-reactive-converter-api-3.0.1.jar, /Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.4/reactive-streams-1.0.4.jar, /Users/<USER>/.m2/repository/io/smallrye/reactive/mutiny/2.6.2/mutiny-2.6.2.jar, /Users/<USER>/.m2/repository/org/jctools/jctools-core/4.0.5/jctools-core-4.0.5.jar, /Users/<USER>/.m2/repository/io/smallrye/reactive/mutiny-zero/1.1.0/mutiny-zero-1.1.0.jar, /Users/<USER>/.m2/repository/io/smallrye/reactive/mutiny-zero-flow-adapters/1.1.0/mutiny-zero-flow-adapters-1.1.0.jar, /Users/<USER>/.m2/repository/org/eclipse/microprofile/health/microprofile-health-api/4.0.1/microprofile-health-api-4.0.1.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-agroal/3.16.2/quarkus-agroal-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-datasource/3.16.2/quarkus-datasource-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-datasource-common/3.16.2/quarkus-datasource-common-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-narayana-jta/3.16.2/quarkus-narayana-jta-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-transaction-annotations/3.16.2/quarkus-transaction-annotations-3.16.2.jar, /Users/<USER>/.m2/repository/io/smallrye/smallrye-context-propagation-jta/2.1.2/smallrye-context-propagation-jta-2.1.2.jar, /Users/<USER>/.m2/repository/io/smallrye/reactive/smallrye-reactive-converter-mutiny/3.0.1/smallrye-reactive-converter-mutiny-3.0.1.jar, /Users/<USER>/.m2/repository/org/jboss/narayana/jta/narayana-jta/7.0.2.Final/narayana-jta-7.0.2.Final.jar, /Users/<USER>/.m2/repository/jakarta/resource/jakarta.resource-api/2.1.0/jakarta.resource-api-2.1.0.jar, /Users/<USER>/.m2/repository/org/jboss/invocation/jboss-invocation/2.0.0.Final/jboss-invocation-2.0.0.Final.jar, /Users/<USER>/.m2/repository/org/eclipse/microprofile/reactive-streams-operators/microprofile-reactive-streams-operators-api/3.0.1/microprofile-reactive-streams-operators-api-3.0.1.jar, /Users/<USER>/.m2/repository/org/jboss/narayana/jts/narayana-jts-integration/7.0.2.Final/narayana-jts-integration-7.0.2.Final.jar, /Users/<USER>/.m2/repository/io/agroal/agroal-api/2.5/agroal-api-2.5.jar, /Users/<USER>/.m2/repository/io/agroal/agroal-narayana/2.5/agroal-narayana-2.5.jar, /Users/<USER>/.m2/repository/org/jboss/jboss-transaction-spi/8.0.0.Final/jboss-transaction-spi-8.0.0.Final.jar, /Users/<USER>/.m2/repository/io/agroal/agroal-pool/2.5/agroal-pool-2.5.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-credentials/3.16.2/quarkus-credentials-3.16.2.jar, /Users/<USER>/.m2/repository/javax/inject/javax.inject/1/javax.inject-1.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-smallrye-fault-tolerance/3.16.2/quarkus-smallrye-fault-tolerance-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-mutiny/3.16.2/quarkus-mutiny-3.16.2.jar, /Users/<USER>/.m2/repository/io/smallrye/reactive/mutiny-smallrye-context-propagation/2.6.2/mutiny-smallrye-context-propagation-2.6.2.jar, /Users/<USER>/.m2/repository/io/smallrye/smallrye-fault-tolerance/6.5.0/smallrye-fault-tolerance-6.5.0.jar, /Users/<USER>/.m2/repository/org/eclipse/microprofile/fault-tolerance/microprofile-fault-tolerance-api/4.1.1/microprofile-fault-tolerance-api-4.1.1.jar, /Users/<USER>/.m2/repository/io/smallrye/smallrye-fault-tolerance-api/6.5.0/smallrye-fault-tolerance-api-6.5.0.jar, /Users/<USER>/.m2/repository/io/smallrye/smallrye-fault-tolerance-core/6.5.0/smallrye-fault-tolerance-core-6.5.0.jar, /Users/<USER>/.m2/repository/io/smallrye/smallrye-fault-tolerance-autoconfig-core/6.5.0/smallrye-fault-tolerance-autoconfig-core-6.5.0.jar, /Users/<USER>/.m2/repository/org/eclipse/microprofile/config/microprofile-config-api/3.1/microprofile-config-api-3.1.jar, /Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/3.0.0/jakarta.annotation-api-3.0.0.jar, /Users/<USER>/.m2/repository/org/jboss/logging/commons-logging-jboss-logging/1.0.0.Final/commons-logging-jboss-logging-1.0.0.Final.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-smallrye-context-propagation/3.16.2/quarkus-smallrye-context-propagation-3.16.2.jar, /Users/<USER>/.m2/repository/io/smallrye/smallrye-context-propagation/2.1.2/smallrye-context-propagation-2.1.2.jar, /Users/<USER>/.m2/repository/io/smallrye/smallrye-context-propagation-api/2.1.2/smallrye-context-propagation-api-2.1.2.jar, /Users/<USER>/.m2/repository/io/smallrye/smallrye-context-propagation-storage/2.1.2/smallrye-context-propagation-storage-2.1.2.jar, /Users/<USER>/.m2/repository/io/smallrye/smallrye-fault-tolerance-context-propagation/6.5.0/smallrye-fault-tolerance-context-propagation-6.5.0.jar, /Users/<USER>/.m2/repository/io/smallrye/smallrye-fault-tolerance-mutiny/6.5.0/smallrye-fault-tolerance-mutiny-6.5.0.jar, /Users/<USER>/.m2/repository/com/rabbitmq/amqp-client/5.25.0/amqp-client-5.25.0.jar, /Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.1.1/jakarta.validation-api-3.1.1.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-container-image-jib/3.16.2/quarkus-container-image-jib-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-container-image/3.16.2/quarkus-container-image-3.16.2.jar, /Users/<USER>/.m2/repository/io/quarkus/quarkus-smallrye-health/3.16.2/quarkus-smallrye-health-3.16.2.jar, /Users/<USER>/.m2/repository/io/smallrye/smallrye-health/4.1.0/smallrye-health-4.1.0.jar, /Users/<USER>/.m2/repository/io/smallrye/smallrye-health-api/4.1.0/smallrye-health-api-4.1.0.jar, /Users/<USER>/.m2/repository/jakarta/json/jakarta.json-api/2.1.3/jakarta.json-api-2.1.3.jar, /Users/<USER>/.m2/repository/io/smallrye/smallrye-health-provided-checks/4.1.0/smallrye-health-provided-checks-4.1.0.jar]
[DEBUG]   (f) useIncrementalCompilation = true
[DEBUG]   (f) useModulePath = true
[DEBUG]   (f) verbose = false
[DEBUG] -- end configuration --
[DEBUG] Using compiler 'javac'.
[INFO] No sources to compile
[ERROR] Using javaTool: /usr/local/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/bin/java
[DEBUG] Maven compiler plugin found, looking for annotation processors
[DEBUG] Found processor paths: []
[DEBUG] Found processors: []
[DEBUG] [io.quarkus.bootstrap.classloading.QuarkusClassLoader.lifecycle] (main) Closing class loader QuarkusClassLoader:Augmentation Class Loader: PROD for quakers_app-1.0.1@5dba3fdc
java.lang.RuntimeException: Created to log a stacktrace
    at io.quarkus.bootstrap.classloading.QuarkusClassLoader.close (QuarkusClassLoader.java:680)
    at io.quarkus.bootstrap.app.CuratedApplication.close (CuratedApplication.java:431)
    at io.quarkus.maven.QuarkusBootstrapProvider$QuarkusMavenAppBootstrap.close (QuarkusBootstrapProvider.java:467)
    at io.quarkus.maven.QuarkusBootstrapProvider.close (QuarkusBootstrapProvider.java:165)
    at io.quarkus.maven.DevMojo.newLauncher (DevMojo.java:1369)
    at io.quarkus.maven.DevMojo$DevModeRunner.<init> (DevMojo.java:1188)
    at io.quarkus.maven.DevMojo.execute (DevMojo.java:466)
    at org.apache.maven.plugin.DefaultBuildPluginManager.executeMojo (DefaultBuildPluginManager.java:126)
    at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute2 (MojoExecutor.java:328)
    at org.apache.maven.lifecycle.internal.MojoExecutor.doExecute (MojoExecutor.java:316)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:212)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:174)
    at org.apache.maven.lifecycle.internal.MojoExecutor.access$000 (MojoExecutor.java:75)
    at org.apache.maven.lifecycle.internal.MojoExecutor$1.run (MojoExecutor.java:162)
    at org.apache.maven.plugin.DefaultMojosExecutionStrategy.execute (DefaultMojosExecutionStrategy.java:39)
    at org.apache.maven.lifecycle.internal.MojoExecutor.execute (MojoExecutor.java:159)
    at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:105)
    at org.apache.maven.lifecycle.internal.LifecycleModuleBuilder.buildProject (LifecycleModuleBuilder.java:73)
    at org.apache.maven.lifecycle.internal.builder.singlethreaded.SingleThreadedBuilder.build (SingleThreadedBuilder.java:53)
    at org.apache.maven.lifecycle.internal.LifecycleStarter.execute (LifecycleStarter.java:118)
    at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:261)
    at org.apache.maven.DefaultMaven.doExecute (DefaultMaven.java:173)
    at org.apache.maven.DefaultMaven.execute (DefaultMaven.java:101)
    at org.apache.maven.cli.MavenCli.execute (MavenCli.java:906)
    at org.apache.maven.cli.MavenCli.doMain (MavenCli.java:283)
    at org.apache.maven.cli.MavenCli.main (MavenCli.java:206)
    at jdk.internal.reflect.DirectMethodHandleAccessor.invoke (DirectMethodHandleAccessor.java:103)
    at java.lang.reflect.Method.invoke (Method.java:580)
    at org.codehaus.plexus.classworlds.launcher.Launcher.launchEnhanced (Launcher.java:255)
    at org.codehaus.plexus.classworlds.launcher.Launcher.launch (Launcher.java:201)
    at org.codehaus.plexus.classworlds.launcher.Launcher.mainWithExitCode (Launcher.java:361)
    at org.codehaus.plexus.classworlds.launcher.Launcher.main (Launcher.java:314)
[DEBUG] Dependency collection stats {ConflictMarker.analyzeTime=688084, ConflictMarker.markTime=101125, ConflictMarker.nodeCount=410, ConflictIdSorter.graphTime=568292, ConflictIdSorter.topsortTime=175958, ConflictIdSorter.conflictIdCount=116, ConflictIdSorter.conflictIdCycleCount=0, ConflictResolver.totalTime=4120291, ConflictResolver.conflictItemCount=295, DfDependencyCollector.collectTime=62781542, DfDependencyCollector.transformTime=5682125}
[ERROR] Executable jar: /Users/<USER>/Desktop/Liden/Backend/quakers_mbs/target/quakers_app-dev.jar
[DEBUG] Launching JVM with command line: /usr/local/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/bin/java -Dquarkus-internal.serialized-app-model.path=/Users/<USER>/Desktop/Liden/Backend/quakers_mbs/target/quarkus/bootstrap/dev-app-model.dat -javaagent:/Users/<USER>/.m2/repository/io/quarkus/quarkus-class-change-agent/3.16.2/quarkus-class-change-agent-3.16.2.jar -XX:TieredStopAtLevel=1 -agentlib:jdwp=transport=dt_socket,address=localhost:5005,server=y,suspend=n -Djava.util.logging.manager=org.jboss.logmanager.LogManager -jar /Users/<USER>/Desktop/Liden/Backend/quakers_mbs/target/quakers_app-dev.jar
Listening for transport dt_socket at address: 5005
LogManager error of type OPEN_FAILURE: Failed to set log file
java.io.FileNotFoundException: /var/log/java/mbs-app.log (No such file or directory)
	at java.base/java.io.FileOutputStream.open0(Native Method)
	at java.base/java.io.FileOutputStream.open(FileOutputStream.java:295)
	at java.base/java.io.FileOutputStream.<init>(FileOutputStream.java:237)
	at org.jboss.logmanager.handlers.FileHandler.setFile(FileHandler.java:155)
	at org.jboss.logmanager.handlers.PeriodicRotatingFileHandler.setFile(PeriodicRotatingFileHandler.java:113)
	at org.jboss.logmanager.handlers.PeriodicSizeRotatingFileHandler.setFileInternal(PeriodicSizeRotatingFileHandler.java:266)
	at org.jboss.logmanager.handlers.PeriodicSizeRotatingFileHandler.setFile(PeriodicSizeRotatingFileHandler.java:174)
	at io.quarkus.runtime.logging.LoggingSetupRecorder.configureFileHandler(LoggingSetupRecorder.java:633)
	at io.quarkus.runtime.logging.LoggingSetupRecorder.initializeLogging(LoggingSetupRecorder.java:174)
	at io.quarkus.deployment.steps.LoggingResourceProcessor$setupLoggingRuntimeInit2024815463.deploy_0(Unknown Source)
	at io.quarkus.deployment.steps.LoggingResourceProcessor$setupLoggingRuntimeInit2024815463.deploy(Unknown Source)
	at io.quarkus.runner.ApplicationImpl.doStart(Unknown Source)
	at io.quarkus.runtime.Application.start(Application.java:101)
	at io.quarkus.runtime.ApplicationLifecycleManager.run(ApplicationLifecycleManager.java:121)
	at io.quarkus.runtime.Quarkus.run(Quarkus.java:71)
	at io.quarkus.runtime.Quarkus.run(Quarkus.java:44)
	at io.quarkus.runtime.Quarkus.run(Quarkus.java:124)
	at io.quarkus.runner.GeneratedMain.main(Unknown Source)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at io.quarkus.runner.bootstrap.StartupActionImpl$1.run(StartupActionImpl.java:116)
	at java.base/java.lang.Thread.run(Thread.java:1575)
LogManager error of type OPEN_FAILURE: Failed to set log file
java.io.FileNotFoundException: /var/log/java/mbs-app.log (No such file or directory)
	at java.base/java.io.FileOutputStream.open0(Native Method)
	at java.base/java.io.FileOutputStream.open(FileOutputStream.java:295)
	at java.base/java.io.FileOutputStream.<init>(FileOutputStream.java:237)
	at org.jboss.logmanager.handlers.FileHandler.setFile(FileHandler.java:155)
	at org.jboss.logmanager.handlers.PeriodicRotatingFileHandler.setFile(PeriodicRotatingFileHandler.java:113)
	at org.jboss.logmanager.handlers.PeriodicSizeRotatingFileHandler.setFileInternal(PeriodicSizeRotatingFileHandler.java:266)
	at org.jboss.logmanager.handlers.PeriodicSizeRotatingFileHandler.setFile(PeriodicSizeRotatingFileHandler.java:174)
	at io.quarkus.runtime.logging.LoggingSetupRecorder.configureFileHandler(LoggingSetupRecorder.java:633)
	at io.quarkus.runtime.logging.LoggingSetupRecorder.initializeLogging(LoggingSetupRecorder.java:174)
	at io.quarkus.deployment.steps.LoggingResourceProcessor$setupLoggingRuntimeInit2024815463.deploy_0(Unknown Source)
	at io.quarkus.deployment.steps.LoggingResourceProcessor$setupLoggingRuntimeInit2024815463.deploy(Unknown Source)
	at io.quarkus.runner.ApplicationImpl.doStart(Unknown Source)
	at io.quarkus.runtime.Application.start(Application.java:101)
	at io.quarkus.runtime.ApplicationLifecycleManager.run(ApplicationLifecycleManager.java:121)
	at io.quarkus.runtime.Quarkus.run(Quarkus.java:71)
	at io.quarkus.runtime.Quarkus.run(Quarkus.java:44)
	at io.quarkus.runtime.Quarkus.run(Quarkus.java:124)
	at io.quarkus.runner.GeneratedMain.main(Unknown Source)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at io.quarkus.runner.bootstrap.StartupActionImpl$1.run(StartupActionImpl.java:116)
	at java.base/java.lang.Thread.run(Thread.java:1575)
[INFO] ------------------------------------------------------------------------
[INFO] BUILD SUCCESS
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  01:34 min
[INFO] Finished at: 2025-06-03T16:33:41+03:00
[INFO] ------------------------------------------------------------------------
[DEBUG] Shutting down adapter factory; available factories [file-lock, rwlock-local, semaphore-local, noop]; available name mappers [discriminating, file-gav, file-hgav, file-static, gav, static]
[DEBUG] Shutting down 'file-lock' factory
[DEBUG] Shutting down 'rwlock-local' factory
[DEBUG] Shutting down 'semaphore-local' factory
[DEBUG] Shutting down 'noop' factory
